# React Frontend Architecture for SIGST

## Technology Stack

### Core Technologies
- **React.js 18.x** - Component library
- **TypeScript** - Type safety and development experience
- **Vite** - Build tool and development server
- **React Router v6** - Client-side routing
- **TanStack Query (React Query)** - Server state management
- **Zustand** - Client state management
- **React Hook Form** - Form handling and validation
- **Zod** - Schema validation

### UI/UX Libraries
- **Tailwind CSS** - Utility-first CSS framework
- **Headless UI** - Unstyled, accessible UI components
- **React Hot Toast** - Notifications
- **Lucide React** - Icons
- **Chart.js + React-Chartjs-2** - Data visualization
- **React Table (TanStack Table)** - Data tables

### Development Tools
- **ESLint + Prettier** - Code formatting and linting
- **Husky** - Git hooks
- **Vitest** - Testing framework
- **React Testing Library** - Component testing

## Project Structure

```
src/
├── components/           # Reusable UI components
│   ├── ui/              # Basic UI components (Button, Input, etc.)
│   ├── forms/           # Form components
│   ├── tables/          # Table components
│   ├── charts/          # Chart components
│   ├── layout/          # Layout components
│   └── common/          # Common components
├── pages/               # Page components
│   ├── auth/           # Authentication pages
│   ├── dashboard/      # Dashboard pages
│   ├── personnel/      # Personnel management pages
│   ├── vehicles/       # Vehicle management pages
│   ├── maintenance/    # Maintenance management pages
│   ├── inventory/      # Inventory management pages
│   ├── tools/          # Tool management pages
│   ├── products/       # Product management pages
│   ├── fuel/           # Fuel management pages
│   └── reports/        # Report pages
├── hooks/              # Custom React hooks
├── services/           # API service functions
├── stores/             # Zustand stores
├── types/              # TypeScript type definitions
├── utils/              # Utility functions
├── constants/          # Application constants
└── styles/             # Global styles and Tailwind config
```

## Component Architecture

### Base UI Components (`/components/ui/`)

#### Button Component
```typescript
// components/ui/Button.tsx
interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'danger' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  children: React.ReactNode;
  onClick?: () => void;
}
```

#### Input Component
```typescript
// components/ui/Input.tsx
interface InputProps {
  type?: 'text' | 'email' | 'password' | 'number' | 'date';
  label?: string;
  placeholder?: string;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  icon?: React.ReactNode;
}
```

#### Modal Component
```typescript
// components/ui/Modal.tsx
interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  children: React.ReactNode;
}
```

#### DataTable Component
```typescript
// components/ui/DataTable.tsx
interface DataTableProps<T> {
  data: T[];
  columns: ColumnDef<T>[];
  pagination?: boolean;
  sorting?: boolean;
  filtering?: boolean;
  loading?: boolean;
  onRowClick?: (row: T) => void;
}
```

### Layout Components (`/components/layout/`)

#### MainLayout
```typescript
// components/layout/MainLayout.tsx
interface MainLayoutProps {
  children: React.ReactNode;
}

// Features:
// - Responsive sidebar navigation
// - Header with user menu and notifications
// - Breadcrumb navigation
// - Main content area
```

#### Sidebar
```typescript
// components/layout/Sidebar.tsx
interface SidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
}

// Navigation Items:
// - Dashboard
// - Personnel Management
// - Vehicle Management
// - Maintenance Management
// - Inventory Management (Parts & Products)
// - Tool Management
// - Fuel Management
// - Reports
```

#### Header
```typescript
// components/layout/Header.tsx
interface HeaderProps {
  title: string;
  breadcrumbs?: BreadcrumbItem[];
  actions?: React.ReactNode;
}

// Features:
// - Page title and breadcrumbs
// - Search functionality
// - Notification bell with count
// - User profile dropdown
// - Theme toggle
```

### Form Components (`/components/forms/`)

#### FormField
```typescript
// components/forms/FormField.tsx
interface FormFieldProps {
  name: string;
  label: string;
  type?: string;
  required?: boolean;
  validation?: any;
  children?: React.ReactNode;
}
```

#### SearchFilter
```typescript
// components/forms/SearchFilter.tsx
interface SearchFilterProps {
  placeholder: string;
  filters?: FilterOption[];
  onSearch: (query: string) => void;
  onFilter: (filters: Record<string, any>) => void;
}
```

### Chart Components (`/components/charts/`)

#### DashboardChart
```typescript
// components/charts/DashboardChart.tsx
interface DashboardChartProps {
  type: 'line' | 'bar' | 'pie' | 'doughnut';
  data: ChartData;
  title: string;
  height?: number;
}
```

## Page Components Architecture

### Dashboard Pages (`/pages/dashboard/`)

#### Overview Dashboard
```typescript
// pages/dashboard/Overview.tsx
// Features:
// - Key performance indicators (KPIs)
// - Recent activities
// - Quick stats cards
// - Interactive charts
// - System alerts
```

#### Vehicle Dashboard
```typescript
// pages/dashboard/VehicleDashboard.tsx
// Features:
// - Fleet overview
// - Maintenance due alerts
// - Fuel consumption trends
// - Vehicle utilization charts
```

### Personnel Pages (`/pages/personnel/`)

#### Employee List
```typescript
// pages/personnel/EmployeeList.tsx
// Features:
// - Employee data table with search/filter
// - Add new employee button
// - Bulk actions
// - Export functionality
```

#### Employee Detail
```typescript
// pages/personnel/EmployeeDetail.tsx
// Features:
// - Personal information
// - Skills management
// - Certifications tracking
// - Training history
// - Performance metrics
```

#### Employee Form
```typescript
// pages/personnel/EmployeeForm.tsx
// Features:
// - Multi-step form
// - Field validation
// - Photo upload
// - Skills assignment
// - Certification upload
```

### Vehicle Pages (`/pages/vehicles/`)

#### Vehicle List
```typescript
// pages/vehicles/VehicleList.tsx
// Features:
// - Vehicle grid/list view
// - Status filtering
// - Maintenance alerts
// - Quick actions
```

#### Vehicle Detail
```typescript
// pages/vehicles/VehicleDetail.tsx
// Features:
// - Vehicle information
// - Document management
// - Maintenance history
// - Fuel consumption
// - Cost analysis
```

### Maintenance Pages (`/pages/maintenance/`)

#### Maintenance Dashboard
```typescript
// pages/maintenance/MaintenanceDashboard.tsx
// Features:
// - Scheduled maintenance calendar
// - Overdue items
// - Technician workload
// - Cost tracking
```

#### Maintenance Request
```typescript
// pages/maintenance/MaintenanceRequest.tsx
// Features:
// - Request form
// - Priority setting
// - Asset selection
// - Photo attachments
```

### Inventory Pages (`/pages/inventory/`)

#### Parts Inventory
```typescript
// pages/inventory/PartsInventory.tsx
// Features:
// - Stock levels overview
// - Low stock alerts
// - Movement history
// - Reorder suggestions
```

#### Stock Management
```typescript
// pages/inventory/StockManagement.tsx
// Features:
// - Stock adjustments
// - Bulk import/export
// - Movement tracking
// - Audit trail
```

## State Management Architecture

### Global State (Zustand)

#### Auth Store
```typescript
// stores/authStore.ts
interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
}
```

#### UI Store
```typescript
// stores/uiStore.ts
interface UIState {
  sidebarCollapsed: boolean;
  theme: 'light' | 'dark';
  notifications: Notification[];
  toggleSidebar: () => void;
  setTheme: (theme: 'light' | 'dark') => void;
  addNotification: (notification: Notification) => void;
  removeNotification: (id: string) => void;
}
```

#### Filter Store
```typescript
// stores/filterStore.ts
interface FilterState {
  activeFilters: Record<string, any>;
  savedFilters: SavedFilter[];
  setFilter: (key: string, value: any) => void;
  clearFilters: () => void;
  saveFilter: (name: string, filters: Record<string, any>) => void;
}
```

### Server State (TanStack Query)

#### API Hooks
```typescript
// hooks/api/useEmployees.ts
export const useEmployees = (params?: EmployeeQueryParams) => {
  return useQuery({
    queryKey: ['employees', params],
    queryFn: () => employeeService.getEmployees(params),
  });
};

export const useCreateEmployee = () => {
  return useMutation({
    mutationFn: employeeService.createEmployee,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] });
    },
  });
};
```

## Service Layer

### API Services

#### Base API Service
```typescript
// services/api.ts
class ApiService {
  private baseURL: string;
  private token: string | null;

  constructor() {
    this.baseURL = import.meta.env.VITE_API_URL;
    this.token = localStorage.getItem('token');
  }

  async request<T>(endpoint: string, options?: RequestInit): Promise<T> {
    // Implementation with error handling, token refresh, etc.
  }

  get<T>(endpoint: string): Promise<T>;
  post<T>(endpoint: string, data: any): Promise<T>;
  put<T>(endpoint: string, data: any): Promise<T>;
  delete<T>(endpoint: string): Promise<T>;
}
```

#### Module Services
```typescript
// services/employeeService.ts
export const employeeService = {
  getEmployees: (params?: EmployeeQueryParams) => 
    api.get<PaginatedResponse<Employee>>('/employees', { params }),
  
  getEmployee: (id: number) => 
    api.get<Employee>(`/employees/${id}`),
  
  createEmployee: (data: CreateEmployeeData) => 
    api.post<Employee>('/employees', data),
  
  updateEmployee: (id: number, data: UpdateEmployeeData) => 
    api.put<Employee>(`/employees/${id}`, data),
  
  deleteEmployee: (id: number) => 
    api.delete(`/employees/${id}`),
};
```

## Custom Hooks

### Data Fetching Hooks
```typescript
// hooks/useTableData.ts
export const useTableData = <T>(
  queryFn: (params: any) => Promise<PaginatedResponse<T>>,
  dependencies: any[] = []
) => {
  const [pagination, setPagination] = useState({ page: 1, limit: 10 });
  const [sorting, setSorting] = useState<SortingState>([]);
  const [filtering, setFiltering] = useState<FilteringState>({});

  const { data, isLoading, error } = useQuery({
    queryKey: ['table-data', pagination, sorting, filtering, ...dependencies],
    queryFn: () => queryFn({ ...pagination, ...sorting, ...filtering }),
  });

  return {
    data: data?.data || [],
    totalCount: data?.pagination.total || 0,
    pagination,
    setPagination,
    sorting,
    setSorting,
    filtering,
    setFiltering,
    isLoading,
    error,
  };
};
```

### Form Hooks
```typescript
// hooks/useFormWithValidation.ts
export const useFormWithValidation = <T extends FieldValues>(
  schema: ZodSchema<T>,
  defaultValues?: T
) => {
  const form = useForm<T>({
    resolver: zodResolver(schema),
    defaultValues,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (
    onSubmit: (data: T) => Promise<void>,
    onError?: (error: any) => void
  ) => {
    return form.handleSubmit(async (data) => {
      setIsSubmitting(true);
      try {
        await onSubmit(data);
      } catch (error) {
        onError?.(error);
      } finally {
        setIsSubmitting(false);
      }
    });
  };

  return {
    ...form,
    isSubmitting,
    handleSubmit,
  };
};
```

## Type Definitions

### Core Types
```typescript
// types/common.ts
export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    current_page: number;
    per_page: number;
    total: number;
    last_page: number;
  };
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
}

export interface ApiError {
  success: false;
  error: {
    code: string;
    message: string;
    details?: Record<string, string[]>;
  };
}
```

### Entity Types
```typescript
// types/entities.ts
export interface Employee {
  id: number;
  employee_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  position: string;
  department: string;
  hire_date: string;
  status: 'active' | 'inactive' | 'terminated';
  created_at: string;
  updated_at: string;
}

export interface Vehicle {
  id: number;
  registration_number: string;
  make: string;
  model: string;
  year: number;
  type: VehicleType;
  status: VehicleStatus;
  current_mileage: number;
  fuel_type: FuelType;
  created_at: string;
  updated_at: string;
}
```

## Routing Structure

```typescript
// App.tsx routing configuration
const router = createBrowserRouter([
  {
    path: '/login',
    element: <LoginPage />,
  },
  {
    path: '/',
    element: <MainLayout />,
    children: [
      {
        index: true,
        element: <DashboardOverview />,
      },
      {
        path: 'personnel',
        children: [
          { index: true, element: <EmployeeList /> },
          { path: 'new', element: <EmployeeForm /> },
          { path: ':id', element: <EmployeeDetail /> },
          { path: ':id/edit', element: <EmployeeForm /> },
        ],
      },
      {
        path: 'vehicles',
        children: [
          { index: true, element: <VehicleList /> },
          { path: 'new', element: <VehicleForm /> },
          { path: ':id', element: <VehicleDetail /> },
          { path: ':id/edit', element: <VehicleForm /> },
        ],
      },
      // ... other routes
    ],
  },
]);
```

## Performance Optimization

### Code Splitting
```typescript
// Lazy loading for routes
const VehicleList = lazy(() => import('../pages/vehicles/VehicleList'));
const VehicleDetail = lazy(() => import('../pages/vehicles/VehicleDetail'));

// Component with Suspense
<Suspense fallback={<LoadingSpinner />}>
  <VehicleList />
</Suspense>
```

### Memoization
```typescript
// Component memoization
export const ExpensiveComponent = memo(({ data }) => {
  const processedData = useMemo(() => {
    return heavyProcessing(data);
  }, [data]);

  return <div>{processedData}</div>;
});
```

### Virtual Scrolling
```typescript
// For large data sets
import { FixedSizeList as List } from 'react-window';

const VirtualizedTable = ({ items }) => (
  <List
    height={600}
    itemCount={items.length}
    itemSize={50}
    itemData={items}
  >
    {({ index, style, data }) => (
      <div style={style}>
        {data[index].name}
      </div>
    )}
  </List>
);
```

## Testing Strategy

### Component Testing
```typescript
// __tests__/components/EmployeeForm.test.tsx
import { render, screen, userEvent } from '@testing-library/react';
import { EmployeeForm } from '../EmployeeForm';

describe('EmployeeForm', () => {
  it('should render form fields', () => {
    render(<EmployeeForm />);
    
    expect(screen.getByLabelText(/first name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
  });

  it('should validate required fields', async () => {
    render(<EmployeeForm />);
    
    await userEvent.click(screen.getByRole('button', { name: /save/i }));
    
    expect(screen.getByText(/first name is required/i)).toBeInTheDocument();
  });
});
```

### Hook Testing
```typescript
// __tests__/hooks/useEmployees.test.tsx
import { renderHook } from '@testing-library/react';
import { useEmployees } from '../hooks/api/useEmployees';

describe('useEmployees', () => {
  it('should fetch employees data', async () => {
    const { result } = renderHook(() => useEmployees());
    
    await waitFor(() => {
      expect(result.current.data).toBeDefined();
    });
  });
});
```

## Error Handling

### Error Boundaries
```typescript
// components/ErrorBoundary.tsx
class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <ErrorFallback />;
    }

    return this.props.children;
  }
}
```

### API Error Handling
```typescript
// utils/errorHandler.ts
export const handleApiError = (error: any) => {
  if (error.response?.status === 401) {
    // Redirect to login
    authStore.logout();
    return;
  }

  if (error.response?.status === 422) {
    // Handle validation errors
    return error.response.data.error.details;
  }

  // Generic error message
  toast.error('An unexpected error occurred');
};
```

This architecture provides a solid foundation for building a scalable, maintainable, and user-friendly React frontend for the SIGST system.
