# SIGST Database Schema Design

## Database: PostgreSQL

### Core Tables Structure

## 1. Personnel Management Module

### employees
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
employee_id (VARCHAR(20), UNIQUE, NOT NULL) -- Custom employee identifier
first_name (VA<PERSON><PERSON><PERSON>(100), NOT NULL)
last_name (VA<PERSON><PERSON><PERSON>(100), NOT NULL)
email (VA<PERSON><PERSON>R(255), UNIQUE, NOT NULL)
phone (VARCHAR(20))
position (VARCHAR(100))
department (VARCHAR(100))
hire_date (DATE, NOT NULL)
status (ENUM: 'active', 'inactive', 'terminated')
emergency_contact_name (VARCHAR(255))
emergency_contact_phone (VARCHAR(20))
address (TEXT)
created_at (TIMESTAMP)
updated_at (TIMESTAMP)
```

### skills
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
name (VA<PERSON><PERSON><PERSON>(100), UNIQUE, NOT NULL)
category (VARCHAR(50))
description (TEXT)
created_at (TIMESTAMP)
updated_at (TIMESTAMP)
```

### employee_skills
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
employee_id (BIGINT, FOREIGN KEY -> employees.id)
skill_id (BIGINT, FOREIGN KEY -> skills.id)
proficiency_level (ENUM: 'beginner', 'intermediate', 'advanced', 'expert')
acquired_date (DATE)
expiry_date (DATE, NULLABLE)
created_at (TIMESTAMP)
updated_at (TIMESTAMP)
```

### certifications
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
name (VARCHAR(255), NOT NULL)
issuing_organization (VARCHAR(255))
description (TEXT)
validity_period_months (INTEGER) -- Duration in months
created_at (TIMESTAMP)
updated_at (TIMESTAMP)
```

### employee_certifications
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
employee_id (BIGINT, FOREIGN KEY -> employees.id)
certification_id (BIGINT, FOREIGN KEY -> certifications.id)
issue_date (DATE, NOT NULL)
expiry_date (DATE, NOT NULL)
certificate_number (VARCHAR(100))
status (ENUM: 'active', 'expired', 'revoked')
created_at (TIMESTAMP)
updated_at (TIMESTAMP)
```

### trainings
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
title (VARCHAR(255), NOT NULL)
description (TEXT)
type (ENUM: 'internal', 'external', 'online', 'workshop')
duration_hours (INTEGER)
cost (DECIMAL(10,2))
created_at (TIMESTAMP)
updated_at (TIMESTAMP)
```

### employee_trainings
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
employee_id (BIGINT, FOREIGN KEY -> employees.id)
training_id (BIGINT, FOREIGN KEY -> trainings.id)
enrollment_date (DATE)
completion_date (DATE, NULLABLE)
status (ENUM: 'enrolled', 'in_progress', 'completed', 'cancelled')
score (DECIMAL(5,2), NULLABLE)
instructor (VARCHAR(255))
created_at (TIMESTAMP)
updated_at (TIMESTAMP)
```

## 2. Vehicle Management Module

### vehicles
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
registration_number (VARCHAR(20), UNIQUE, NOT NULL)
make (VARCHAR(50), NOT NULL)
model (VARCHAR(50), NOT NULL)
year (INTEGER, NOT NULL)
vin (VARCHAR(17), UNIQUE)
type (ENUM: 'car', 'truck', 'van', 'motorcycle', 'equipment')
status (ENUM: 'active', 'maintenance', 'out_of_service', 'sold')
purchase_date (DATE)
purchase_price (DECIMAL(12,2))
current_mileage (INTEGER)
fuel_type (ENUM: 'gasoline', 'diesel', 'electric', 'hybrid')
capacity (VARCHAR(50)) -- Passenger or cargo capacity
created_at (TIMESTAMP)
updated_at (TIMESTAMP)
```

### vehicle_documents
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
vehicle_id (BIGINT, FOREIGN KEY -> vehicles.id)
document_type (ENUM: 'insurance', 'registration', 'technical_inspection', 'permit')
document_number (VARCHAR(100))
issue_date (DATE, NOT NULL)
expiry_date (DATE, NOT NULL)
issuing_authority (VARCHAR(255))
file_path (VARCHAR(500)) -- Path to uploaded document
status (ENUM: 'valid', 'expired', 'pending_renewal')
created_at (TIMESTAMP)
updated_at (TIMESTAMP)
```

### vehicle_interventions
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
vehicle_id (BIGINT, FOREIGN KEY -> vehicles.id)
type (ENUM: 'maintenance', 'repair', 'accident', 'inspection')
date (DATE, NOT NULL)
description (TEXT, NOT NULL)
cost (DECIMAL(10,2))
mileage_at_intervention (INTEGER)
technician_id (BIGINT, FOREIGN KEY -> employees.id)
supplier (VARCHAR(255))
status (ENUM: 'completed', 'in_progress', 'scheduled')
created_at (TIMESTAMP)
updated_at (TIMESTAMP)
```

### vehicle_accidents
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
vehicle_id (BIGINT, FOREIGN KEY -> vehicles.id)
driver_id (BIGINT, FOREIGN KEY -> employees.id)
accident_date (DATE, NOT NULL)
location (VARCHAR(500))
description (TEXT, NOT NULL)
severity (ENUM: 'minor', 'moderate', 'severe')
police_report_number (VARCHAR(100))
insurance_claim_number (VARCHAR(100))
repair_cost (DECIMAL(10,2))
status (ENUM: 'reported', 'under_investigation', 'resolved')
created_at (TIMESTAMP)
updated_at (TIMESTAMP)
```

## 3. Maintenance Management Module

### maintenance_schedules
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
asset_type (ENUM: 'vehicle', 'tool', 'equipment')
asset_id (BIGINT) -- References to vehicles.id, tools.id, etc.
maintenance_type (VARCHAR(100), NOT NULL)
frequency_type (ENUM: 'mileage', 'time', 'usage_hours')
frequency_value (INTEGER) -- Miles, days, or hours
last_maintenance_date (DATE)
next_due_date (DATE, NOT NULL)
description (TEXT)
estimated_cost (DECIMAL(10,2))
priority (ENUM: 'low', 'medium', 'high', 'critical')
status (ENUM: 'scheduled', 'overdue', 'completed', 'cancelled')
created_at (TIMESTAMP)
updated_at (TIMESTAMP)
```

### maintenance_requests
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
request_number (VARCHAR(20), UNIQUE, NOT NULL)
requester_id (BIGINT, FOREIGN KEY -> employees.id)
asset_type (ENUM: 'vehicle', 'tool', 'equipment')
asset_id (BIGINT)
request_type (ENUM: 'corrective', 'preventive', 'emergency')
priority (ENUM: 'low', 'medium', 'high', 'emergency')
description (TEXT, NOT NULL)
requested_date (DATE, NOT NULL)
required_date (DATE)
assigned_technician_id (BIGINT, FOREIGN KEY -> employees.id)
status (ENUM: 'pending', 'approved', 'in_progress', 'completed', 'rejected')
estimated_cost (DECIMAL(10,2))
actual_cost (DECIMAL(10,2))
completion_date (DATE)
created_at (TIMESTAMP)
updated_at (TIMESTAMP)
```

### maintenance_operations
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
request_id (BIGINT, FOREIGN KEY -> maintenance_requests.id)
operation_date (DATE, NOT NULL)
technician_id (BIGINT, FOREIGN KEY -> employees.id)
operation_type (VARCHAR(100))
description (TEXT, NOT NULL)
duration_hours (DECIMAL(4,2))
parts_used (JSON) -- Array of spare parts used
tools_used (JSON) -- Array of tools used
labor_cost (DECIMAL(8,2))
parts_cost (DECIMAL(8,2))
total_cost (DECIMAL(10,2))
quality_check_passed (BOOLEAN, DEFAULT false)
notes (TEXT)
created_at (TIMESTAMP)
updated_at (TIMESTAMP)
```

## 4. Spare Parts Management Module

### spare_parts
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
part_number (VARCHAR(50), UNIQUE, NOT NULL)
name (VARCHAR(255), NOT NULL)
description (TEXT)
category (VARCHAR(100))
manufacturer (VARCHAR(100))
supplier (VARCHAR(100))
unit_price (DECIMAL(8,2), NOT NULL)
currency (VARCHAR(3), DEFAULT 'USD')
unit_of_measure (VARCHAR(20)) -- pieces, liters, kg, etc.
minimum_stock_level (INTEGER, NOT NULL)
maximum_stock_level (INTEGER)
reorder_point (INTEGER, NOT NULL)
lead_time_days (INTEGER)
storage_location (VARCHAR(100))
is_active (BOOLEAN, DEFAULT true)
created_at (TIMESTAMP)
updated_at (TIMESTAMP)
```

### parts_inventory
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
part_id (BIGINT, FOREIGN KEY -> spare_parts.id)
current_stock (INTEGER, NOT NULL, DEFAULT 0)
reserved_stock (INTEGER, DEFAULT 0) -- Stock allocated for scheduled maintenance
available_stock (INTEGER, GENERATED) -- current_stock - reserved_stock
last_updated (TIMESTAMP, DEFAULT CURRENT_TIMESTAMP)
```

### parts_movements
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
part_id (BIGINT, FOREIGN KEY -> spare_parts.id)
movement_type (ENUM: 'in', 'out', 'adjustment', 'transfer')
quantity (INTEGER, NOT NULL)
unit_cost (DECIMAL(8,2))
total_cost (DECIMAL(10,2))
reference_type (ENUM: 'purchase_order', 'maintenance_request', 'adjustment', 'transfer')
reference_id (BIGINT) -- References PO, maintenance request, etc.
employee_id (BIGINT, FOREIGN KEY -> employees.id)
notes (TEXT)
movement_date (DATE, NOT NULL)
created_at (TIMESTAMP)
updated_at (TIMESTAMP)
```

### parts_suppliers
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
name (VARCHAR(255), NOT NULL)
contact_person (VARCHAR(255))
email (VARCHAR(255))
phone (VARCHAR(20))
address (TEXT)
payment_terms (VARCHAR(100))
delivery_time_days (INTEGER)
rating (DECIMAL(2,1)) -- 1.0 to 5.0
is_active (BOOLEAN, DEFAULT true)
created_at (TIMESTAMP)
updated_at (TIMESTAMP)
```

## 5. Tool Management Module

### tools
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
tool_number (VARCHAR(50), UNIQUE, NOT NULL)
name (VARCHAR(255), NOT NULL)
description (TEXT)
category (VARCHAR(100))
manufacturer (VARCHAR(100))
model (VARCHAR(100))
serial_number (VARCHAR(100))
purchase_date (DATE)
purchase_price (DECIMAL(10,2))
warranty_expiry (DATE)
condition (ENUM: 'excellent', 'good', 'fair', 'poor', 'out_of_service')
location (VARCHAR(100))
assigned_to_employee_id (BIGINT, FOREIGN KEY -> employees.id, NULLABLE)
calibration_required (BOOLEAN, DEFAULT false)
last_calibration_date (DATE)
next_calibration_date (DATE)
maintenance_schedule_id (BIGINT, FOREIGN KEY -> maintenance_schedules.id, NULLABLE)
status (ENUM: 'available', 'in_use', 'maintenance', 'calibration', 'lost', 'disposed')
created_at (TIMESTAMP)
updated_at (TIMESTAMP)
```

### tool_assignments
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
tool_id (BIGINT, FOREIGN KEY -> tools.id)
employee_id (BIGINT, FOREIGN KEY -> employees.id)
assigned_date (DATE, NOT NULL)
expected_return_date (DATE)
actual_return_date (DATE, NULLABLE)
purpose (VARCHAR(255))
condition_at_assignment (ENUM: 'excellent', 'good', 'fair', 'poor')
condition_at_return (ENUM: 'excellent', 'good', 'fair', 'poor', NULLABLE)
notes (TEXT)
status (ENUM: 'active', 'returned', 'overdue')
created_at (TIMESTAMP)
updated_at (TIMESTAMP)
```

## 6. Product Management Module

### products
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
product_code (VARCHAR(50), UNIQUE, NOT NULL)
name (VARCHAR(255), NOT NULL)
description (TEXT)
category (VARCHAR(100))
manufacturer (VARCHAR(100))
unit_of_measure (VARCHAR(20))
unit_price (DECIMAL(8,2))
has_expiry (BOOLEAN, DEFAULT false)
shelf_life_days (INTEGER, NULLABLE) -- For products with expiry
storage_conditions (TEXT)
minimum_stock_level (INTEGER, NOT NULL)
maximum_stock_level (INTEGER)
reorder_point (INTEGER, NOT NULL)
is_active (BOOLEAN, DEFAULT true)
created_at (TIMESTAMP)
updated_at (TIMESTAMP)
```

### product_inventory
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
product_id (BIGINT, FOREIGN KEY -> products.id)
batch_number (VARCHAR(100))
manufacture_date (DATE)
expiry_date (DATE, NULLABLE)
current_stock (INTEGER, NOT NULL, DEFAULT 0)
reserved_stock (INTEGER, DEFAULT 0)
available_stock (INTEGER, GENERATED)
location (VARCHAR(100))
supplier_id (BIGINT, FOREIGN KEY -> parts_suppliers.id)
purchase_price (DECIMAL(8,2))
status (ENUM: 'good', 'near_expiry', 'expired', 'damaged')
created_at (TIMESTAMP)
updated_at (TIMESTAMP)
```

### product_consumption
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
product_id (BIGINT, FOREIGN KEY -> products.id)
inventory_id (BIGINT, FOREIGN KEY -> product_inventory.id)
consumed_by_employee_id (BIGINT, FOREIGN KEY -> employees.id)
quantity_consumed (INTEGER, NOT NULL)
consumption_date (DATE, NOT NULL)
purpose (VARCHAR(255))
project_reference (VARCHAR(100))
notes (TEXT)
created_at (TIMESTAMP)
updated_at (TIMESTAMP)
```

## 7. Fuel Management Module

### fuel_stations
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
name (VARCHAR(255), NOT NULL)
address (TEXT)
fuel_types (JSON) -- Array of available fuel types
contact_phone (VARCHAR(20))
is_active (BOOLEAN, DEFAULT true)
created_at (TIMESTAMP)
updated_at (TIMESTAMP)
```

### fuel_transactions
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
vehicle_id (BIGINT, FOREIGN KEY -> vehicles.id)
driver_id (BIGINT, FOREIGN KEY -> employees.id)
fuel_station_id (BIGINT, FOREIGN KEY -> fuel_stations.id)
transaction_date (DATE, NOT NULL)
fuel_type (ENUM: 'gasoline', 'diesel', 'electric')
quantity (DECIMAL(8,2), NOT NULL) -- Liters or kWh
unit_price (DECIMAL(6,3), NOT NULL)
total_amount (DECIMAL(8,2), NOT NULL)
odometer_reading (INTEGER)
receipt_number (VARCHAR(100))
payment_method (ENUM: 'cash', 'card', 'fuel_card', 'account')
created_at (TIMESTAMP)
updated_at (TIMESTAMP)
```

### fuel_efficiency
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
vehicle_id (BIGINT, FOREIGN KEY -> vehicles.id)
period_start_date (DATE, NOT NULL)
period_end_date (DATE, NOT NULL)
total_fuel_consumed (DECIMAL(8,2))
total_distance (INTEGER) -- km
efficiency_rate (DECIMAL(6,2)) -- km per liter or km per kWh
average_efficiency (DECIMAL(6,2)) -- Monthly average
created_at (TIMESTAMP)
updated_at (TIMESTAMP)
```

## 8. System Management Tables

### users
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
username (VARCHAR(100), UNIQUE, NOT NULL)
email (VARCHAR(255), UNIQUE, NOT NULL)
email_verified_at (TIMESTAMP, NULLABLE)
password (VARCHAR(255), NOT NULL)
employee_id (BIGINT, FOREIGN KEY -> employees.id, NULLABLE)
role (ENUM: 'admin', 'manager', 'technician', 'user')
permissions (JSON) -- Array of specific permissions
is_active (BOOLEAN, DEFAULT true)
last_login_at (TIMESTAMP, NULLABLE)
remember_token (VARCHAR(100), NULLABLE)
created_at (TIMESTAMP)
updated_at (TIMESTAMP)
```

### notifications
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
user_id (BIGINT, FOREIGN KEY -> users.id)
type (VARCHAR(100), NOT NULL) -- maintenance_due, stock_low, document_expiry, etc.
title (VARCHAR(255), NOT NULL)
message (TEXT, NOT NULL)
data (JSON, NULLABLE) -- Additional notification data
read_at (TIMESTAMP, NULLABLE)
created_at (TIMESTAMP)
updated_at (TIMESTAMP)
```

### system_alerts
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
alert_type (VARCHAR(100), NOT NULL)
entity_type (VARCHAR(50)) -- vehicle, tool, part, etc.
entity_id (BIGINT)
severity (ENUM: 'info', 'warning', 'critical')
message (VARCHAR(500), NOT NULL)
is_resolved (BOOLEAN, DEFAULT false)
resolved_at (TIMESTAMP, NULLABLE)
resolved_by_user_id (BIGINT, FOREIGN KEY -> users.id, NULLABLE)
created_at (TIMESTAMP)
updated_at (TIMESTAMP)
```

### reports
```sql
id (BIGINT, PRIMARY KEY, AUTO_INCREMENT)
name (VARCHAR(255), NOT NULL)
type (VARCHAR(100), NOT NULL)
parameters (JSON) -- Report parameters and filters
generated_by_user_id (BIGINT, FOREIGN KEY -> users.id)
generated_at (TIMESTAMP, NOT NULL)
file_path (VARCHAR(500)) -- Path to generated report file
file_size (BIGINT)
scheduled (BOOLEAN, DEFAULT false)
schedule_frequency (ENUM: 'daily', 'weekly', 'monthly', 'quarterly', NULLABLE)
last_sent_at (TIMESTAMP, NULLABLE)
created_at (TIMESTAMP)
updated_at (TIMESTAMP)
```

## Indexes for Performance

### Primary Indexes (Automatically created with PRIMARY KEY)
- All tables have primary key indexes on id column

### Foreign Key Indexes (For relationship performance)
```sql
-- Personnel Module
CREATE INDEX idx_employee_skills_employee_id ON employee_skills(employee_id);
CREATE INDEX idx_employee_skills_skill_id ON employee_skills(skill_id);
CREATE INDEX idx_employee_certifications_employee_id ON employee_certifications(employee_id);
CREATE INDEX idx_employee_trainings_employee_id ON employee_trainings(employee_id);

-- Vehicle Module  
CREATE INDEX idx_vehicle_documents_vehicle_id ON vehicle_documents(vehicle_id);
CREATE INDEX idx_vehicle_interventions_vehicle_id ON vehicle_interventions(vehicle_id);
CREATE INDEX idx_vehicle_accidents_vehicle_id ON vehicle_accidents(vehicle_id);

-- Maintenance Module
CREATE INDEX idx_maintenance_requests_requester_id ON maintenance_requests(requester_id);
CREATE INDEX idx_maintenance_operations_request_id ON maintenance_operations(request_id);

-- Parts Module
CREATE INDEX idx_parts_movements_part_id ON parts_movements(part_id);
CREATE INDEX idx_parts_inventory_part_id ON parts_inventory(part_id);

-- Tools Module
CREATE INDEX idx_tool_assignments_tool_id ON tool_assignments(tool_id);
CREATE INDEX idx_tool_assignments_employee_id ON tool_assignments(employee_id);

-- Products Module
CREATE INDEX idx_product_inventory_product_id ON product_inventory(product_id);
CREATE INDEX idx_product_consumption_product_id ON product_consumption(product_id);

-- Fuel Module
CREATE INDEX idx_fuel_transactions_vehicle_id ON fuel_transactions(vehicle_id);
CREATE INDEX idx_fuel_efficiency_vehicle_id ON fuel_efficiency(vehicle_id);
```

### Search and Filter Indexes
```sql
-- Text search indexes
CREATE INDEX idx_employees_name ON employees(first_name, last_name);
CREATE INDEX idx_employees_email ON employees(email);
CREATE INDEX idx_vehicles_registration ON vehicles(registration_number);
CREATE INDEX idx_spare_parts_name ON spare_parts(name);
CREATE INDEX idx_tools_name ON tools(name);

-- Status and date indexes
CREATE INDEX idx_employees_status ON employees(status);
CREATE INDEX idx_vehicles_status ON vehicles(status);
CREATE INDEX idx_maintenance_requests_status ON maintenance_requests(status);
CREATE INDEX idx_vehicle_documents_expiry ON vehicle_documents(expiry_date);
CREATE INDEX idx_employee_certifications_expiry ON employee_certifications(expiry_date);

-- Stock level indexes
CREATE INDEX idx_parts_inventory_stock_levels ON parts_inventory(current_stock, available_stock);
CREATE INDEX idx_product_inventory_stock_levels ON product_inventory(current_stock, available_stock);
```

## Key Relationships Summary

1. **employees** ↔ **skills** (Many-to-Many via employee_skills)
2. **employees** ↔ **certifications** (Many-to-Many via employee_certifications)  
3. **employees** ↔ **trainings** (Many-to-Many via employee_trainings)
4. **vehicles** → **vehicle_documents** (One-to-Many)
5. **vehicles** → **vehicle_interventions** (One-to-Many)
6. **spare_parts** → **parts_inventory** (One-to-One)
7. **spare_parts** → **parts_movements** (One-to-Many)
8. **tools** → **tool_assignments** (One-to-Many)
9. **products** → **product_inventory** (One-to-Many)
10. **vehicles** → **fuel_transactions** (One-to-Many)
11. **users** → **employees** (One-to-One, nullable)
12. **users** → **notifications** (One-to-Many)
