<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreEmployeeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'employee_id' => [
                'nullable',
                'string',
                'max:20',
                Rule::unique('employees', 'employee_id')
            ],
            'first_name' => [
                'required',
                'string',
                'max:100',
                'regex:/^[a-zA-Z\s\-\'\.]+$/'
            ],
            'last_name' => [
                'required',
                'string',
                'max:100',
                'regex:/^[a-zA-Z\s\-\'\.]+$/'
            ],
            'email' => [
                'required',
                'email',
                'max:255',
                Rule::unique('employees', 'email')
            ],
            'phone' => [
                'nullable',
                'string',
                'max:20',
                'regex:/^[\+]?[0-9\-\(\)\s]+$/'
            ],
            'position' => [
                'nullable',
                'string',
                'max:100'
            ],
            'department' => [
                'nullable',
                'string',
                'max:100'
            ],
            'hire_date' => [
                'required',
                'date',
                'before_or_equal:today'
            ],
            'status' => [
                'nullable',
                Rule::in(['active', 'inactive', 'terminated'])
            ],
            'emergency_contact_name' => [
                'nullable',
                'string',
                'max:255'
            ],
            'emergency_contact_phone' => [
                'nullable',
                'string',
                'max:20',
                'regex:/^[\+]?[0-9\-\(\)\s]+$/'
            ],
            'address' => [
                'nullable',
                'string',
                'max:1000'
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'employee_id' => 'employee ID',
            'first_name' => 'first name',
            'last_name' => 'last name',
            'hire_date' => 'hire date',
            'emergency_contact_name' => 'emergency contact name',
            'emergency_contact_phone' => 'emergency contact phone',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'first_name.regex' => 'The first name may only contain letters, spaces, hyphens, apostrophes, and periods.',
            'last_name.regex' => 'The last name may only contain letters, spaces, hyphens, apostrophes, and periods.',
            'phone.regex' => 'The phone number format is invalid.',
            'emergency_contact_phone.regex' => 'The emergency contact phone number format is invalid.',
            'hire_date.before_or_equal' => 'The hire date cannot be in the future.',
        ];
    }
}
