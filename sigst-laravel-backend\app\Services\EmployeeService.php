<?php

namespace App\Services;

use App\Models\Employee;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class EmployeeService
{
    /**
     * Create a new employee.
     */
    public function createEmployee(array $data): Employee
    {
        return DB::transaction(function () use ($data) {
            $employee = Employee::create($data);

            // Create user account if email is provided
            if (isset($data['email'])) {
                $this->createUserAccount($employee);
            }

            return $employee;
        });
    }

    /**
     * Update an existing employee.
     */
    public function updateEmployee(Employee $employee, array $data): Employee
    {
        return DB::transaction(function () use ($employee, $data) {
            $employee->update($data);

            // Update user account if email changed
            if (isset($data['email']) && $employee->user) {
                $employee->user->update([
                    'email' => $data['email']
                ]);
            }

            return $employee->fresh();
        });
    }

    /**
     * Delete an employee (soft delete).
     */
    public function deleteEmployee(Employee $employee): bool
    {
        return DB::transaction(function () use ($employee) {
            // Deactivate user account
            if ($employee->user) {
                $employee->user->update(['is_active' => false]);
            }

            // Update employee status
            $employee->update(['status' => 'terminated']);

            // Soft delete employee
            return $employee->delete();
        });
    }

    /**
     * Assign skill to employee.
     */
    public function assignSkillToEmployee(Employee $employee, int $skillId, array $pivotData): void
    {
        $employee->skills()->attach($skillId, array_merge($pivotData, [
            'created_at' => now(),
            'updated_at' => now()
        ]));
    }

    /**
     * Remove skill from employee.
     */
    public function removeSkillFromEmployee(Employee $employee, int $skillId): void
    {
        $employee->skills()->detach($skillId);
    }

    /**
     * Update employee skill proficiency.
     */
    public function updateEmployeeSkill(Employee $employee, int $skillId, array $pivotData): void
    {
        $employee->skills()->updateExistingPivot($skillId, array_merge($pivotData, [
            'updated_at' => now()
        ]));
    }

    /**
     * Assign certification to employee.
     */
    public function assignCertificationToEmployee(Employee $employee, int $certificationId, array $pivotData): void
    {
        $employee->certifications()->attach($certificationId, array_merge($pivotData, [
            'created_at' => now(),
            'updated_at' => now()
        ]));
    }

    /**
     * Update employee certification.
     */
    public function updateEmployeeCertification(Employee $employee, int $certificationId, array $pivotData): void
    {
        $employee->certifications()->updateExistingPivot($certificationId, array_merge($pivotData, [
            'updated_at' => now()
        ]));
    }

    /**
     * Enroll employee in training.
     */
    public function enrollEmployeeInTraining(Employee $employee, int $trainingId, array $pivotData): void
    {
        $employee->trainings()->attach($trainingId, array_merge($pivotData, [
            'created_at' => now(),
            'updated_at' => now()
        ]));
    }

    /**
     * Complete employee training.
     */
    public function completeEmployeeTraining(Employee $employee, int $trainingId, array $completionData): void
    {
        $employee->trainings()->updateExistingPivot($trainingId, array_merge($completionData, [
            'status' => 'completed',
            'completion_date' => now(),
            'updated_at' => now()
        ]));
    }

    /**
     * Get employees by department.
     */
    public function getEmployeesByDepartment(string $department): Collection
    {
        return Employee::byDepartment($department)
            ->active()
            ->with(['skills', 'certifications'])
            ->get();
    }

    /**
     * Get employees by position.
     */
    public function getEmployeesByPosition(string $position): Collection
    {
        return Employee::byPosition($position)
            ->active()
            ->with(['skills', 'certifications'])
            ->get();
    }

    /**
     * Get employees with specific skill.
     */
    public function getEmployeesWithSkill(int $skillId, string $minProficiency = null): Collection
    {
        $query = Employee::whereHas('skills', function ($q) use ($skillId, $minProficiency) {
            $q->where('skill_id', $skillId);
            
            if ($minProficiency) {
                $proficiencyLevels = ['beginner', 'intermediate', 'advanced', 'expert'];
                $minIndex = array_search($minProficiency, $proficiencyLevels);
                $allowedLevels = array_slice($proficiencyLevels, $minIndex);
                $q->whereIn('proficiency_level', $allowedLevels);
            }
        });

        return $query->active()
            ->with(['skills' => function ($q) use ($skillId) {
                $q->where('skill_id', $skillId)->withPivot(['proficiency_level', 'acquired_date']);
            }])
            ->get();
    }

    /**
     * Get employees with expiring certifications.
     */
    public function getEmployeesWithExpiringCertifications(int $days = 30): Collection
    {
        return Employee::withExpiringCertifications($days)
            ->with(['certifications' => function ($query) use ($days) {
                $query->wherePivot('expiry_date', '<=', now()->addDays($days))
                      ->wherePivot('status', 'active')
                      ->withPivot(['expiry_date', 'certificate_number', 'status']);
            }])
            ->get();
    }

    /**
     * Get employee performance metrics.
     */
    public function getEmployeePerformanceMetrics(Employee $employee): array
    {
        return [
            'completed_trainings' => $employee->trainings()
                ->wherePivot('status', 'completed')
                ->count(),
            'active_certifications' => $employee->certifications()
                ->wherePivot('status', 'active')
                ->count(),
            'skills_count' => $employee->skills()->count(),
            'maintenance_operations' => $employee->maintenanceOperations()->count(),
            'tool_assignments' => $employee->toolAssignments()->count(),
            'years_of_service' => $employee->hire_date->diffInYears(now()),
        ];
    }

    /**
     * Create user account for employee.
     */
    private function createUserAccount(Employee $employee): User
    {
        $username = strtolower($employee->first_name . '.' . $employee->last_name);
        
        // Ensure unique username
        $originalUsername = $username;
        $counter = 1;
        while (User::where('username', $username)->exists()) {
            $username = $originalUsername . $counter;
            $counter++;
        }

        return User::create([
            'username' => $username,
            'email' => $employee->email,
            'password' => Hash::make('password'), // Default password - should be changed on first login
            'employee_id' => $employee->id,
            'role' => 'user',
            'is_active' => true,
        ]);
    }

    /**
     * Generate employee performance report.
     */
    public function generatePerformanceReport(Employee $employee): array
    {
        $metrics = $this->getEmployeePerformanceMetrics($employee);
        
        $recentTrainings = $employee->trainings()
            ->wherePivot('completion_date', '>=', now()->subMonths(12))
            ->wherePivot('status', 'completed')
            ->withPivot(['completion_date', 'score'])
            ->get();

        $activeCertifications = $employee->certifications()
            ->wherePivot('status', 'active')
            ->withPivot(['issue_date', 'expiry_date', 'certificate_number'])
            ->get();

        $skillsAssessment = $employee->skills()
            ->withPivot(['proficiency_level', 'acquired_date'])
            ->get()
            ->groupBy('pivot.proficiency_level');

        return [
            'employee' => $employee,
            'metrics' => $metrics,
            'recent_trainings' => $recentTrainings,
            'active_certifications' => $activeCertifications,
            'skills_by_proficiency' => $skillsAssessment,
            'generated_at' => now(),
        ];
    }
}
