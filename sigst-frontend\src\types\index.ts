// Base types
export interface BaseEntity {
  id: number;
  created_at: string;
  updated_at: string;
}

// User and Authentication types
export interface User extends BaseEntity {
  username: string;
  email: string;
  email_verified_at?: string;
  employee_id?: number;
  role: 'admin' | 'manager' | 'technician' | 'user';
  permissions: string[];
  is_active: boolean;
  last_login_at?: string;
  employee?: Employee;
}

export interface LoginCredentials {
  email: string;
  password: string;
  remember?: boolean;
}

export interface AuthResponse {
  user: User;
  token: string;
  expires_in: number;
}

// Personnel Management types
export interface Employee extends BaseEntity {
  employee_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  position?: string;
  department?: string;
  hire_date: string;
  status: 'active' | 'inactive' | 'terminated';
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  address?: string;
  skills?: EmployeeSkill[];
  certifications?: EmployeeCertification[];
  trainings?: EmployeeTraining[];
}

export interface Skill extends BaseEntity {
  name: string;
  category?: string;
  description?: string;
}

export interface EmployeeSkill extends BaseEntity {
  employee_id: number;
  skill_id: number;
  proficiency_level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  acquired_date?: string;
  expiry_date?: string;
  skill?: Skill;
}

export interface Certification extends BaseEntity {
  name: string;
  issuing_organization?: string;
  description?: string;
  validity_period_months?: number;
}

export interface EmployeeCertification extends BaseEntity {
  employee_id: number;
  certification_id: number;
  issue_date: string;
  expiry_date: string;
  certificate_number?: string;
  status: 'active' | 'expired' | 'revoked';
  certification?: Certification;
}

export interface Training extends BaseEntity {
  title: string;
  description?: string;
  type: 'internal' | 'external' | 'online' | 'workshop';
  duration_hours?: number;
  cost?: number;
}

export interface EmployeeTraining extends BaseEntity {
  employee_id: number;
  training_id: number;
  enrollment_date?: string;
  completion_date?: string;
  status: 'enrolled' | 'in_progress' | 'completed' | 'cancelled';
  score?: number;
  instructor?: string;
  training?: Training;
}

// Vehicle Management types
export interface Vehicle extends BaseEntity {
  registration_number: string;
  make: string;
  model: string;
  year: number;
  vin?: string;
  type: 'car' | 'truck' | 'van' | 'motorcycle' | 'equipment';
  status: 'active' | 'maintenance' | 'out_of_service' | 'sold';
  purchase_date?: string;
  purchase_price?: number;
  current_mileage: number;
  fuel_type: 'gasoline' | 'diesel' | 'electric' | 'hybrid';
  capacity?: string;
  documents?: VehicleDocument[];
  interventions?: VehicleIntervention[];
  accidents?: VehicleAccident[];
}

export interface VehicleDocument extends BaseEntity {
  vehicle_id: number;
  document_type: 'insurance' | 'registration' | 'technical_inspection' | 'permit';
  document_number?: string;
  issue_date: string;
  expiry_date: string;
  issuing_authority?: string;
  file_path?: string;
  status: 'valid' | 'expired' | 'pending_renewal';
}

export interface VehicleIntervention extends BaseEntity {
  vehicle_id: number;
  type: 'maintenance' | 'repair' | 'accident' | 'inspection';
  date: string;
  description: string;
  cost?: number;
  mileage_at_intervention?: number;
  technician_id?: number;
  supplier?: string;
  status: 'completed' | 'in_progress' | 'scheduled';
  technician?: Employee;
}

export interface VehicleAccident extends BaseEntity {
  vehicle_id: number;
  driver_id?: number;
  accident_date: string;
  location?: string;
  description: string;
  severity: 'minor' | 'moderate' | 'severe';
  police_report_number?: string;
  insurance_claim_number?: string;
  repair_cost?: number;
  status: 'reported' | 'under_investigation' | 'resolved';
  driver?: Employee;
}

// Maintenance Management types
export interface MaintenanceSchedule extends BaseEntity {
  asset_type: 'vehicle' | 'tool' | 'equipment';
  asset_id: number;
  maintenance_type: string;
  frequency_type: 'mileage' | 'time' | 'usage_hours';
  frequency_value: number;
  last_maintenance_date?: string;
  next_due_date: string;
  description?: string;
  estimated_cost?: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'scheduled' | 'overdue' | 'completed' | 'cancelled';
}

export interface MaintenanceRequest extends BaseEntity {
  request_number: string;
  requester_id?: number;
  asset_type: 'vehicle' | 'tool' | 'equipment';
  asset_id: number;
  request_type: 'corrective' | 'preventive' | 'emergency';
  priority: 'low' | 'medium' | 'high' | 'emergency';
  description: string;
  requested_date: string;
  required_date?: string;
  assigned_technician_id?: number;
  status: 'pending' | 'approved' | 'in_progress' | 'completed' | 'rejected';
  estimated_cost?: number;
  actual_cost?: number;
  completion_date?: string;
  requester?: Employee;
  assigned_technician?: Employee;
  operations?: MaintenanceOperation[];
}

export interface MaintenanceOperation extends BaseEntity {
  request_id: number;
  operation_date: string;
  technician_id?: number;
  operation_type?: string;
  description: string;
  duration_hours?: number;
  parts_used?: any;
  tools_used?: any;
  labor_cost?: number;
  parts_cost?: number;
  total_cost?: number;
  quality_check_passed: boolean;
  notes?: string;
  technician?: Employee;
}

// Spare Parts types
export interface SparePart extends BaseEntity {
  part_number: string;
  name: string;
  description?: string;
  category?: string;
  manufacturer?: string;
  supplier?: string;
  unit_price: number;
  currency: string;
  unit_of_measure?: string;
  minimum_stock_level: number;
  maximum_stock_level?: number;
  reorder_point: number;
  lead_time_days?: number;
  storage_location?: string;
  is_active: boolean;
  inventory?: PartInventory;
}

export interface PartInventory extends BaseEntity {
  part_id: number;
  current_stock: number;
  reserved_stock: number;
  available_stock: number;
  last_updated: string;
}

export interface PartMovement extends BaseEntity {
  part_id: number;
  movement_type: 'in' | 'out' | 'adjustment' | 'transfer';
  quantity: number;
  unit_cost?: number;
  total_cost?: number;
  reference_type?: 'purchase_order' | 'maintenance_request' | 'adjustment' | 'transfer';
  reference_id?: number;
  employee_id?: number;
  notes?: string;
  movement_date: string;
  employee?: Employee;
  part?: SparePart;
}

export interface PartSupplier extends BaseEntity {
  name: string;
  contact_person?: string;
  email?: string;
  phone?: string;
  address?: string;
  payment_terms?: string;
  delivery_time_days?: number;
  rating?: number;
  is_active: boolean;
}

// Tool Management types
export interface Tool extends BaseEntity {
  tool_number: string;
  name: string;
  description?: string;
  category?: string;
  manufacturer?: string;
  model?: string;
  serial_number?: string;
  purchase_date?: string;
  purchase_price?: number;
  warranty_expiry?: string;
  condition: 'excellent' | 'good' | 'fair' | 'poor' | 'out_of_service';
  location?: string;
  assigned_to_employee_id?: number;
  calibration_required: boolean;
  last_calibration_date?: string;
  next_calibration_date?: string;
  maintenance_schedule_id?: number;
  status: 'available' | 'in_use' | 'maintenance' | 'calibration' | 'lost' | 'disposed';
  assigned_to?: Employee;
  assignments?: ToolAssignment[];
}

export interface ToolAssignment extends BaseEntity {
  tool_id: number;
  employee_id: number;
  assigned_date: string;
  expected_return_date?: string;
  actual_return_date?: string;
  purpose?: string;
  condition_at_assignment: 'excellent' | 'good' | 'fair' | 'poor';
  condition_at_return?: 'excellent' | 'good' | 'fair' | 'poor';
  notes?: string;
  status: 'active' | 'returned' | 'overdue';
  tool?: Tool;
  employee?: Employee;
}

// Product Management types
export interface Product extends BaseEntity {
  product_code: string;
  name: string;
  description?: string;
  category?: string;
  manufacturer?: string;
  unit_of_measure?: string;
  unit_price?: number;
  has_expiry: boolean;
  shelf_life_days?: number;
  storage_conditions?: string;
  minimum_stock_level: number;
  maximum_stock_level?: number;
  reorder_point: number;
  is_active: boolean;
  inventory?: ProductInventory[];
}

export interface ProductInventory extends BaseEntity {
  product_id: number;
  batch_number?: string;
  manufacture_date?: string;
  expiry_date?: string;
  current_stock: number;
  reserved_stock: number;
  available_stock: number;
  location?: string;
  supplier_id?: number;
  purchase_price?: number;
  status: 'good' | 'near_expiry' | 'expired' | 'damaged';
  supplier?: PartSupplier;
}

export interface ProductConsumption extends BaseEntity {
  product_id: number;
  inventory_id: number;
  consumed_by_employee_id?: number;
  quantity_consumed: number;
  consumption_date: string;
  purpose?: string;
  project_reference?: string;
  notes?: string;
  product?: Product;
  inventory?: ProductInventory;
  consumed_by?: Employee;
}

// Fuel Management types
export interface FuelStation extends BaseEntity {
  name: string;
  address?: string;
  fuel_types: string[];
  contact_phone?: string;
  is_active: boolean;
}

export interface FuelTransaction extends BaseEntity {
  vehicle_id: number;
  driver_id?: number;
  fuel_station_id?: number;
  transaction_date: string;
  fuel_type: 'gasoline' | 'diesel' | 'electric';
  quantity: number;
  unit_price: number;
  total_amount: number;
  odometer_reading?: number;
  receipt_number?: string;
  payment_method: 'cash' | 'card' | 'fuel_card' | 'account';
  vehicle?: Vehicle;
  driver?: Employee;
  fuel_station?: FuelStation;
}

export interface FuelEfficiency extends BaseEntity {
  vehicle_id: number;
  period_start_date: string;
  period_end_date: string;
  total_fuel_consumed?: number;
  total_distance?: number;
  efficiency_rate?: number;
  average_efficiency?: number;
  vehicle?: Vehicle;
}

// System Management types
export interface Notification extends BaseEntity {
  user_id: number;
  type: string;
  title: string;
  message: string;
  data?: any;
  read_at?: string;
}

export interface SystemAlert extends BaseEntity {
  alert_type: string;
  entity_type?: string;
  entity_id?: number;
  severity: 'info' | 'warning' | 'critical';
  message: string;
  is_resolved: boolean;
  resolved_at?: string;
  resolved_by_user_id?: number;
  resolved_by?: User;
}

export interface Report extends BaseEntity {
  name: string;
  type: string;
  parameters?: any;
  generated_by_user_id?: number;
  generated_at: string;
  file_path?: string;
  file_size?: number;
  scheduled: boolean;
  schedule_frequency?: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  last_sent_at?: string;
  generated_by?: User;
}

// Form types
export interface CreateEmployeeForm {
  employee_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  position?: string;
  department?: string;
  hire_date: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  address?: string;
}

export interface CreateVehicleForm {
  registration_number: string;
  make: string;
  model: string;
  year: number;
  vin?: string;
  type: 'car' | 'truck' | 'van' | 'motorcycle' | 'equipment';
  purchase_date?: string;
  purchase_price?: number;
  current_mileage: number;
  fuel_type: 'gasoline' | 'diesel' | 'electric' | 'hybrid';
  capacity?: string;
}

// Dashboard types
export interface DashboardStats {
  employees: {
    total: number;
    active: number;
    inactive: number;
    new_this_month: number;
  };
  vehicles: {
    total: number;
    active: number;
    in_maintenance: number;
    out_of_service: number;
  };
  maintenance: {
    pending_requests: number;
    overdue_schedules: number;
    completed_this_month: number;
    total_cost_this_month: number;
  };
  inventory: {
    low_stock_parts: number;
    expired_products: number;
    total_parts_value: number;
    recent_movements: number;
  };
}

// Search and filter types
export interface SearchFilters {
  search?: string;
  status?: string;
  department?: string;
  position?: string;
  type?: string;
  category?: string;
  date_from?: string;
  date_to?: string;
  page?: number;
  per_page?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

// Common utility types
export interface SelectOption {
  value: string | number;
  label: string;
  disabled?: boolean;
}

export interface TableColumn<T = any> {
  key: string;
  title: string;
  sortable?: boolean;
  width?: string;
  render?: (value: any, record: T) => React.ReactNode;
}

export interface PaginationInfo {
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
  from: number;
  to: number;
}
