<!DOCTYPE HTML>
<html>
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="Author" content="MMclaughlin">
  <title>decimal.js-light API</title>
  <style>
html{font-size:100%}
body{background:#fff;font-family:"Helvetica Neue",Helvetica,Arial,sans-serif;font-size:13px;
  line-height:1.65em;min-height:100%;margin:0}
body,i{color:#000}
.nav{background:#fff;position:fixed;top:0;bottom:0;left:0;width:210px;overflow-y:auto;
  padding:15px 0 30px 15px}
div.container{width:600px;margin:50px 0 50px 240px}
p{margin:0 0 1em;width:600px}
pre,ul{margin:1em 0}
h1,h2,h3,h4,h5{margin:0;padding:1.5em 0 0}
h1,h2{padding:.75em 0}
h1{font:400 3em Consolas, monaco, monospace;color:#000;margin-bottom:1em}
h2{font-size:2.25em;color:#f00}
h3{font-size:1.75em;color:#69d2e7}
h4{font-size:1.75em;color:#f00;padding-bottom:.75em}
h5{font-size:1.2em;margin-bottom:.4em}
h6{font-size:1.1em;margin-bottom:0.8em;padding:0.5em 0}
dd dt{font-size:1.2em}
dt{padding-top:.5em}
dd{padding-top:.35em}
b{font-weight:700}
a,a:visited{color:#f00;text-decoration:none}
a:active,a:hover{outline:0;text-decoration:underline}
.nav a,.nav b,.nav a:visited{display:block;color:#f00;font-weight:700;margin-top:15px}
.nav b{color:#69d2e7;margin-top:20px;cursor:default;width:auto}
ul{list-style-type:none;padding:0 0 0 20px}
.nav ul{line-height:14px;padding-left:0;margin:5px 0 0}
.nav ul a,.nav ul a:visited,span{display:inline;color:#000;font-family:Verdana,Geneva,sans-serif;
  font-size:11px;font-weight:400;margin:0}
.inset{margin-left:20px;font-size:.9em}
.nav li{width:auto;margin:0 0 3px}
.alias{font-style:italic;margin-left:20px}
table{border-collapse:collapse;border-spacing:0;border:2px solid #a7dbd8;margin:1.75em 0;padding:0}
td,th{text-align:left;margin:0;padding:2px 5px;border:1px dotted #a7dbd8}
th{border-top:2px solid #a7dbd8;border-bottom:2px solid #a7dbd8;color:#f00}
code,pre{font-family:Consolas, monaco, monospace;font-weight:400}
pre{background:#f5f5f5;white-space:pre-wrap;word-wrap:break-word;border-left:5px solid #a7dbd8;
  padding:1px 0 1px 15px;margin:1.2em 0}
code,.nav-title{color:#f00}
.end{margin-bottom:25px}
.centre{text-align:center}
#modes,#configProps{color:#f00}
.spacer{line-height:0px}
#faq{margin:3em 0 0}
li span{float:right;margin-right:10px;color:#c0c0c0}
#js{font:inherit;color:#f00}
  </style>
</head>
<body>

  <div class="nav">

    <a class='nav-title' href="#">API</a>

    <b>CONSTRUCTOR</b>
    <ul><li><a href="#decimal">Decimal</a></li></ul>

    <a href="#methods">Methods</a>
    <ul>
      <li><a href="#Dclone" >clone</a></li>
      <li><a href="#Dconfig">config</a></li>
    </ul>

    <a href="#constructor-properties">Properties</a>
    <ul>
      <li><a href="#precision">precision</a></li>
      <li><a href="#rounding" >rounding</a></li>
      <li><a href="#toExpNeg" >toExpNeg</a></li>
      <li><a href="#toExpPos" >toExpPos</a></li>
      <li><a href="#ln10"     >LN10</a></li>
      <li class='spacer'>&nbsp;</li>
      <li><a href="#modes">ROUND_UP</a></li>
      <li><a href="#modes">ROUND_DOWN</a></li>
      <li><a href="#modes">ROUND_CEIL</a></li>
      <li><a href="#modes">ROUND_FLOOR</a></li>
      <li><a href="#modes">ROUND_HALF_UP</a></li>
      <li><a href="#modes">ROUND_HALF_DOWN</a></li>
      <li><a href="#modes">ROUND_HALF_EVEN</a></li>
      <li><a href="#modes">ROUND_HALF_CEIL</a></li>
      <li><a href="#modes">ROUND_HALF_FLOOR</a></li>
      <li><a href="#modes">EUCLID</a></li>
    </ul>

    <b> INSTANCE </b>

    <a href="#prototype-methods">Methods</a>
    <ul>
      <li><a href="#abs"          >absoluteValue           </a><span>abs</span>  </li>
      <li><a href="#cmp"          >comparedTo              </a><span>cmp</span>  </li>
      <li><a href="#dp"           >decimalPlaces           </a><span>dp</span>   </li>
      <li><a href="#div"          >dividedBy               </a><span>div</span>  </li>
      <li><a href="#idiv"         >dividedToIntegerBy      </a><span>idiv</span> </li>
      <li><a href="#eq"           >equals                  </a><span>eq</span>   </li>
      <li><a href="#exp"          >exponent                </a>                  </li>
      <li><a href="#gt"           >greaterThan             </a><span>gt</span>   </li>
      <li><a href="#gte"          >greaterThanOrEqualTo    </a><span>gte</span>  </li>
      <li><a href="#isint"        >isInteger               </a><span>isint</span></li>
      <li><a href="#isneg"        >isNegative              </a><span>isneg</span></li>
      <li><a href="#ispos"        >isPositive              </a><span>ispos</span></li>
      <li><a href="#isZero"       >isZero                  </a>                  </li>
      <li><a href="#lt"           >lessThan                </a><span>lt</span>   </li>
      <li><a href="#lte"          >lessThanOrEqualTo       </a><span>lte</span>  </li>
      <li><a href="#log"          >logarithm               </a><span>log</span>  </li>
      <li><a href="#sub"          >minus                   </a><span>sub</span>  </li>
      <li><a href="#mod"          >modulo                  </a><span>mod</span>  </li>
      <li><a href="#exp"          >naturalExponential      </a><span>exp</span>  </li>
      <li><a href="#ln"           >naturalLogarithm        </a><span>ln</span>   </li>
      <li><a href="#neg"          >negated                 </a><span>neg</span>  </li>
      <li><a href="#add"          >plus                    </a><span>add</span>  </li>
      <li><a href="#sd"           >precision               </a><span>sd</span>   </li>
      <li><a href="#sqrt"         >squareRoot              </a><span>sqrt</span> </li>
      <li><a href="#mul"          >times                   </a><span>mul</span>  </li>
      <li><a href="#todp"         >toDecimalPlaces         </a><span>todp</span> </li>
      <li><a href="#toExponential">toExponential           </a>                  </li>
      <li><a href="#toFixed"      >toFixed                 </a>                  </li>
      <li><a href="#toInteger"    >toInteger               </a><span>toint</span></li>
      <li><a href="#toJSON"       >toJSON                  </a>                  </li>
      <li><a href="#toNumber"     >toNumber                </a>                  </li>
      <li><a href="#pow"          >toPower                 </a><span>pow</span>  </li>
      <li><a href="#toPrecision"  >toPrecision             </a>                  </li>
      <li><a href="#tosd"         >toSignificantDigits     </a><span>tosd</span> </li>
      <li><a href="#toString"     >toString                </a>                  </li>
      <li><a href="#valueOf"      >valueOf                 </a><span>val</span>  </li>
    </ul>

    <a href="#instance-properties">Properties</a>
    <ul>
      <li><a href="#digits"  >d</a><span>digits</span></li>
      <li><a href="#exponent">e</a><span>exponent</span></li>
      <li><a href="#sign"    >s</a><span>sign</span></li>
    </ul>

    <a href="#Errors">Errors</a>
    <a class='end' href="#faq">FAQ</a>

  </div>

  <div class="container">

    <h1>decimal<span id='js'>.js</span>-light</h1>

    <p>
      The light version of <a href='https://github.com/MikeMcl/decimal.js/'>decimal.js</a>, an
      arbitrary-precision Decimal type for JavaScript.
    </p>
    <p><a href='https://github.com/MikeMcl/decimal.js-light'>Hosted on GitHub</a>.</p>

    <h2>API</h2>

    <p>
      See the <a href='https://github.com/MikeMcl/decimal.js'>README</a> on GitHub for a quick-start
      introduction.
    </p>
    <p>
      In all examples below, <code>var</code> and semicolons are not shown, and if a commented-out
      value is in quotes it means <code>toString</code> has been called on the preceding expression.
    </p><br />
    <p>
      When the library is loaded, it defines a single function object,
      <a href='#decimal'><code>Decimal</code></a>, the constructor of Decimal instances.
    </p>
    <p>
      <i>
        If necessary, multiple Decimal constructors can be created, each with their own independent
        configuration, e.g. precision and range, which applies to all Decimal instances created from
        it.
      </i>
    </p>
    <p>
      <i>
        A new Decimal constructor is created by calling the <code><a href='#Dclone'>clone</a></code>
        method of an already existing Decimal constructor.
      </i>
    </p>



    <h3 class='end'>CONSTRUCTOR</h3>

    <h5 id="decimal">
      Decimal<code class='inset'>Decimal(value) <i>&rArr; Decimal</i></code>
    </h5>
    <dl>
      <dt><code>value</code>: <i>number|string|Decimal</i></dt>
      <dd>
        Integer or float.
      </dd>
      <dd>
        The number of digits is not limited, except by JavaScript's maximum array size and, in
        practice, the processing time required.
      </dd>
      <dd>
        The maximum permissible exponent magnitude is approximately <code>9007199254740991</code>.
      </dd>
      <dd>
        String values may be in exponential (floating-point), as well as normal (fixed-point)
        notation.
      </dd>
      <dd>
        In exponential notation, <code>e</code> or <code>E</code> defines a power-of-ten exponent.
      </dd>
    </dl>
    <p>Returns a new Decimal object instance.</p>
    <p>Throws on an invalid <code>value</code>.</p>
    <pre>
x = new Decimal(9)                       // '9'
y = new Decimal(x)                       // '9'

new Decimal('5032485723458348569331745.33434346346912144534543')
new Decimal('4.321e+4')                  // '43210'
new Decimal('-735.0918e-430')            // '-7.350918e-428'
new Decimal('5.6700000')                 // '5.67'
new Decimal('.5')                        // '0.5'

new Decimal(0.046875)                    // '0.046875'
new Decimal('0.046875000000')            // '0.046875'

new Decimal(4.6875e-2)                   // '0.046875'
new Decimal('468.75e-4')                 // '0.046875'</pre>



    <h4 id="methods">Methods</h4>
    <p>The methods of a Decimal constructor.</p>



    <h5 id="Dclone">
      clone
      <code class='inset'>.clone([object]) <i>&rArr; Decimal constructor</i></code>
    </h5>
    <p><code>object</code>: <i>object</i></p>
    <p>
      Returns a new independent Decimal constructor with configuration settings as described by
      <code>object</code> (see <a href='#Dconfig'><code>config</code></a>), or with the same
      settings as <code>this</code> Decimal constructor if <code>object</code> is omitted.
    </p>
    <pre>Decimal.config({ precision: 5 })
D9 = Decimal.clone({ precision: 9 })

a = new Decimal(1)
b = new D9(1)

a.div(3)                           // 0.33333
b.div(3)                           // 0.333333333

// D9 = Decimal.clone({ precision: 9 }) is equivalent to:
D9 = Decimal.clone()
D9.config({ precision: 9 })</pre>
    <p>
      It is not inefficient in terms of memory usage to use multiple Decimal constructors as
      functions are shared between them.
    </p>



    <h5 id="Dconfig">
      config<code class='inset'>.set(object) <i>&rArr; Decimal constructor</i></code>
    </h5>
    <p><code>object</code>: <i>object</i></p>
    <p>
      Configures the 'global' settings for <code>this</code> particular Decimal constructor, i.e.
      the settings which apply to operations performed on the Decimal instances created by it.
    </p>
    <p>Returns <code>this</code> Decimal constructor.</p>
    <p>
      The configuration object, <code>object</code>, can contain some or all of the properties
      described in detail at <a href="#constructor-properties">Properties</a> and shown in the
      example below.
    </p>
    <p>
      The values of the configuration object properties are checked for validity and then stored as
      equivalently-named properties of <code>this</code> Decimal constructor.
    </p>
    <p>Throws on an invalid <code>object</code> or configuration property value.</p>
    <pre>
// Defaults
Decimal.config({
    precision: 20,
    rounding: 4,
    toExpNeg: -7,
    toExpPos: 21,
    LN10: new Decimal('2.30258509299404568401799145468436...')
})

Decimal.set({ rounding: Decimal.ROUND_CEIL })
</pre>
    <p>
      The properties of a Decimal constructor can also be set by direct assignment, but that will
      by-pass the validity checking that this method performs - which is not a problem if the user
      knows that the checks are unnecessary.
    </p>



    <h4 id="constructor-properties">Properties</h4>
    <p>The properties of a Decimal constructor.</p>



    <h6 id='configProps'>Configuration properties</h6>
    <p>
      The values of the configuration properties <a href='#precision'><code>precision</code></a>,
      <a href='#rounding'><code>rounding</code></a>, <a href='#toExpNeg'><code>toExpNeg</code></a>
      and <a href='#toExpPos'><code>toExpPos</code></a> are set using the
      <a href='#Dconfig'><code>config</code></a> method.
    </p>
    <p>
      As simple object properties they can be set directly without using
      <a href='#Dconfig'><code>config</code></a>, and it is fine to do so, but the values assigned
      will not then be checked for validity. For example:
    </p>
    <pre>Decimal.config({ precision: 0 })
// '[DecimalError] Invalid argument: precision: 0'

Decimal.precision = 0
// No error is thrown and the results of calculations are unreliable</pre>



    <h5 id="precision">precision</h5>
    <p>
      <i>number</i>: integer, <code>1</code> to <code>1e+9</code> inclusive<br />
      Default value: <code>20</code>
    </p>
    <p>The <i>maximum</i> number of significant digits of the result of an operation.</p>
    <p>
      All functions which return a Decimal will return the value to <code>precision</code>
      significant digits except <a href='#decimal'><code>Decimal</code></a>,
      <a href='#abs'><code>absoluteValue</code></a>,
      <a href='#neg'><code>negated</code></a>, <a href='#round'><code>toInteger</code></a>, and
      <a href='#todp'><code>toDecimalPlaces</code></a>.
    </p>
    <pre>Decimal.config({ precision: 5 })
Decimal.precision                  // 5</pre>



    <h5 id="rounding">rounding</h5>
    <p>
      <i>number</i>: integer, <code>0</code> to <code>8</code> inclusive<br />
      Default value: <code>4</code> <a href="#modes">(<code>ROUND_HALF_UP</code>)</a>
    </p>
    <p>
      The default rounding mode used by <a href='#round'><code>toInteger</code></a>,
      <a href='#todp'><code>toDecimalPlaces</code></a>,
      <a href='#toExponential'><code>toExponential</code></a>,
      <a href='#toFixed'><code>toFixed</code></a>,
      <a href='#toPrecision'><code>toPrecision</code></a> and
      <a href='#tosd'><code>toSignificantDigits</code></a>.
    </p>
    <p>
      The <a href='#modes'>rounding modes</a> are available as enumerated properties of the
      constructor.
    </p>
    <pre>Decimal.config({ rounding: Decimal.ROUND_UP })
Decimal.config({ rounding: 0 })    // equivalent
Decimal.rounding                   // 0</pre>



    <h5 id="toExpNeg">toExpNeg</h5>
    <p>
      <i>number</i>: integer, <code>-9e15</code> to <code>0</code> inclusive<br />
      Default value: <code>-7</code>
    </p>
    <p>
      The negative exponent value at and below which <a href='#toString'><code>toString</code></a>
      returns exponential notation.
    </p>
    <pre>Decimal.config({ toExpNeg: -7 })
Decimal.toExpNeg                   // -7
new Decimal(0.00000123)            // '0.00000123'       e is -6
new Decimal(0.000000123)           // '1.23e-7'

// Always return exponential notation:
Decimal.config({ toExpNeg: 0 })</pre>
    <p>
      JavaScript numbers use exponential notation for negative exponents of <code>-7</code> and
      below.
    </p>
    <p>
      Regardless of the value of <code>toExpNeg</code>, the
      <a href='#toFixed'><code>toFixed</code></a> method will always return a value in normal
      notation and the <a href='#toExponential'><code>toExponential</code></a> method will always
      return a value in exponential form.
    </p>



    <h5 id="toExpPos">toExpPos</h5>
    <p>
      <i>number</i>: integer, <code>0</code> to <code>9e15</code> inclusive<br />
      Default value: <code>20</code>
    </p>
    <p>
      The positive exponent value at and above which <a href='#toString'><code>toString</code></a>
      returns exponential notation.
    </p>
    <pre>Decimal.config({ toExpPos: 2 })
Decimal.toExpPos                   // 2
new Decimal(12.3)                  // '12.3'        e is 1
new Decimal(123)                   // '1.23e+2'

// Always return exponential notation:
Decimal.config({ toExpPos: 0 })</pre>
    <p>
      JavaScript numbers use exponential notation for positive exponents of <code>20</code> and
      above.
    </p>
    <p>
      Regardless of the value of <code>toExpPos</code>, the
      <a href='#toFixed'><code>toFixed</code></a> method will always return a value in normal
      notation and the <a href='#toExponential'><code>toExponential</code></a> method will always
      return a value in exponential form.
    </p>



    <h5 id="ln10">LN10</h5>
    <p>
      <i>string|Decimal</i>: the natural logarithm of <code>10</code><br />
      The default value has <code>115</code> digits
    </p>
    <p>
      The maximum precision of the <a href='#exp'><code>naturalExponential</code></a>,
      <a href='#ln'><code>naturalLogarithm</code></a>, <a href='#log'><code>logarithm</code></a>,
      and <a href='#pow'><code>toPower</code></a> methods is determined by the precision of the
      value of <code>LN10</code>.
    </p>
    <p>
      The default value of <code>LN10</code> enables a maximum precision of about <code>100</code>
      digits. To increase this, assign a new value to <code>LN10</code> using a string or Decimal
      value with about 15 digits more than the maximum precision required.
    </p>
    <p>
      An error will be thrown if the <code>LN10</code> value does not have sufficient precision to
      enable an operation to be performed.
    </p>
    <pre>
Decimal.config({ LN10: '2.3025850929940456840179914546843642076011014886287729760333279009' })

Decimal.LN10.toFixed(5)             // ''2.30259'</pre>



    <h6 id="modes">Rounding modes</h6>
    <p>
      The library's enumerated rounding modes are stored as properties of the Decimal constructor.
      <br />They are not referenced internally by the library itself.
    </p>
    <p>Rounding modes 0 to 6 (inclusive) are the same as those of Java's BigDecimal class.</p>
    <table>
      <tr><th>Property</th><th>Value</th><th>Description</th></tr>
      <tr><td><b>ROUND_UP</b></td><td class='centre'>0</td><td>Rounds away from zero</td></tr>
      <tr><td><b>ROUND_DOWN</b></td><td class='centre'>1</td><td>Rounds towards zero</td></tr>
      <tr><td><b>ROUND_CEIL</b></td><td class='centre'>2</td><td>Rounds towards Infinity</td></tr>
      <tr><td><b>ROUND_FLOOR</b></td><td class='centre'>3</td><td>Rounds towards -Infinity</td></tr>
      <tr>
        <td><b>ROUND_HALF_UP</b></td><td class='centre'>4</td>
        <td>Rounds towards nearest neighbour.<br />If equidistant, rounds away from zero</td>
      </tr>
      <tr>
        <td><b>ROUND_HALF_DOWN</b></td><td class='centre'>5</td>
        <td>Rounds towards nearest neighbour.<br />If equidistant, rounds towards zero</td>
      </tr>
      <tr>
        <td><b>ROUND_HALF_EVEN</b></td><td class='centre'>6</td>
        <td>
          Rounds towards nearest neighbour.<br />If equidistant, rounds towards even neighbour
        </td>
      </tr>
      <tr>
        <td><b>ROUND_HALF_CEIL</b></td><td class='centre'>7</td>
        <td>Rounds towards nearest neighbour.<br />If equidistant, rounds towards Infinity</td>
      </tr>
      <tr>
        <td><b>ROUND_HALF_FLOOR</b></td><td class='centre'>8</td>
        <td>Rounds towards nearest neighbour.<br />If equidistant, rounds towards -Infinity</td>
      </tr>
    </table>
    <pre>Decimal.config({ rounding: Decimal.ROUND_CEIL })
Decimal.config({ rounding: 2 })    // equivalent
Decimal.rounding                   // 2</pre>




    <h3>INSTANCE</h3>

    <h4 id="prototype-methods">Methods</h4>
    <p>The methods inherited by a Decimal instance from its constructor's prototype object.</p>
    <p>A Decimal instance is immutable in the sense that it is not changed by its methods.</p>
    <p>Methods that return a Decimal can be chained:</p>
    <pre>x = new Decimal(2).times('999.999999999999999').dividedBy(4).toFixed(2)</pre>
    <p>Methods do not round their arguments before execution.</p>
    <p>
      Many method names have a shorter alias. (Internally, the library always uses the shorter
      method names.)
    </p>



    <h5 id="abs">absoluteValue<code class='inset'>.abs() <i>&rArr; Decimal</i></code></h5>
    <p>
      Returns a new Decimal whose value is the absolute value, i.e. the magnitude, of the value of
      this Decimal.
    </p>
    <p>
      The return value is not affected by the value of the
      <a href='#precision'><code>precision</code></a> setting.
    </p>
    <pre>
x = new Decimal(-0.8)
y = x.absoluteValue()         // '0.8'
z = y.abs()                   // '0.8'</pre>



    <h5 id="cmp">comparedTo<code class='inset'>.cmp(x) <i>&rArr; number</i></code></h5>
    <p><code>x</code>: <i>number|string|Decimal</i></p>
    <table>
      <tr><th>Returns</th><th>&nbsp;</th></tr>
      <tr>
        <td class='centre'><code>1</code></td>
        <td>if the value of this Decimal is greater than the value of <code>x</code></td>
      </tr>
      <tr>
        <td class='centre'><code>-1</code></td>
        <td>if the value of this Decimal is less than the value of <code>x</code></td>
      </tr>
      <tr>
        <td class='centre'><code>0</code></td>
        <td>if this Decimal and <code>x</code> have the same value</td>
      </tr>
    </table>
    <pre>
x = new Decimal(4)
y = new Decimal(5)
x.comparedTo(y)                // -1
x.comparedTo(x.plus(1))        // 0</pre>



    <h5 id="dp">decimalPlaces<code class='inset'>.dp() <i>&rArr; number</i></code></h5>
    <p>
      Returns the number of decimal places, i.e. the number of digits after the decimal point, of
      the value of this Decimal.
    </p>
    <pre>
x = new Decimal(1.234)
x.decimalPlaces()              // '3'
y = new Decimal(987.654321)
y.dp()                         // '6'</pre>



    <h5 id="div">dividedBy<code class='inset'>.div(x) <i>&rArr; Decimal</i></code></h5>
    <p><code>x</code>: <i>number|string|Decimal</i></p>
    <p>
      Returns a new Decimal whose value is the value of this Decimal divided by <code>x</code>,
      truncated to <a href='#precision'><code>precision</code></a> significant digits.
    </p>
    <pre>
x = new Decimal(355)
y = new Decimal(113)
x.dividedBy(y)             // '3.14159292035398230088'
x.div(5)                   // '71'</pre>



    <h5 id="idiv">
      dividedToIntegerBy<code class='inset'>.idiv(x) <i>&rArr; Decimal</i></code>
    </h5>
    <p><code>x</code>: <i>number|string|Decimal</i></p>
    <p>
      Return a new Decimal whose value is the integer part of dividing this Decimal by
      <code>x</code>, truncated to <code><a href='#precision'>precision</a></code> significant
      digits.
    </p>
    <pre>
x = new Decimal(5)
y = new Decimal(3)
x.dividedToIntegerBy(y)     // '1'
x.idiv(0.7)             // '7'</pre>



    <h5 id="eq">equals<code class='inset'>.eq(x) <i>&rArr; boolean</i></code></h5>
    <p><code>x</code>: <i>number|string|Decimal</i></p>
    <p>
      Returns <code>true</code> if the value of this Decimal equals the value of <code>x</code>,
      otherwise returns <code>false</code>.
    </p>
    <p>Note: This method uses the <code>cmp</code> method internally.</p>
    <pre>
0 === 1e-324                     // true
x = new Decimal(0)
x.equals('1e-324')               // false</pre>



    <h5 id="exp">exponent<code class='inset'>.exponent() <i>&rArr; number</i></code></h5>
    <p>Returns the exponent value of this Decimal.</p>
    <pre>
x = new Decimal(1234.567)
x.exponent()                     // 3</pre>



    <h5 id="gt">greaterThan<code class='inset'>.gt(x) <i>&rArr; boolean</i></code></h5>
    <p><code>x</code>: <i>number|string|Decimal</i></p>
    <p>
      Returns <code>true</code> if the value of this Decimal is greater than the value of
      <code>x</code>, otherwise returns <code>false</code>.
    </p>
    <p>Note: This method uses the <code>cmp</code> method internally.</p>
    <pre>
0.1 &gt; (0.3 - 0.2)                            // true
x = new Decimal(0.1)
x.greaterThan(Decimal(0.3).minus(0.2))       // false
new Decimal(0).gt(x)                         // false</pre>



    <h5 id="gte">
      greaterThanOrEqualTo<code class='inset'>.gte(x) <i>&rArr; boolean</i></code>
    </h5>
    <p><code>x</code>: <i>number|string|Decimal</i></p>
    <p>
      Returns <code>true</code> if the value of this Decimal is greater than or equal to the value
      of <code>x</code>, otherwise returns <code>false</code>.
    </p>
    <p>Note: This method uses the <code>cmp</code> method internally.</p>
    <pre>
(0.3 - 0.2) &gt;= 0.1                       // false
x = new Decimal(0.3).minus(0.2)
x.greaterThanOrEqualTo(0.1)              // true
new Decimal(1).gte(x)                    // true</pre>



    <h5 id="isint">isInteger<code class='inset'>.isint() <i>&rArr; boolean</i></code></h5>
    <p>
      Returns <code>true</code> if the value of this Decimal is a whole number, otherwise returns
      <code>false</code>.
    </p>
    <pre>
x = new Decimal(1)
x.isInteger()                            // true
y = new Decimal(123.456)
y.isint()                                // false</pre>



    <h5 id="isneg">isNegative<code class='inset'>.isneg() <i>&rArr; boolean</i></code></h5>
    <p>
      Returns <code>true</code> if the value of this Decimal is negative, otherwise returns
      <code>false</code>.
    </p>
    <pre>
x = new Decimal(0)
x.isNegative()                           // false
y = new Decimal(2)
y.isneg                                  // false</pre>
    <p>Note: <code>n &lt; 0</code> can be used if <code>n &lt;= -Number.MIN_VALUE</code>.</p>



     <h5 id="ispos">isPositive<code class='inset'>.ispos() <i>&rArr; boolean</i></code></h5>
    <p>
      Returns <code>true</code> if the value of this Decimal is positive, otherwise returns
      <code>false</code>.
    </p>
    <pre>
x = new Decimal(0)
x.isPositive()                           // false
y = new Decimal(-2)
y.ispos                                  // false</pre>
    <p>Note: <code>n &lt; 0</code> can be used if <code>n &lt;= -Number.MIN_VALUE</code>.</p>



    <h5 id="isZero">isZero<code class='inset'>.isZero() <i>&rArr; boolean</i></code></h5>
    <p>
      Returns <code>true</code> if the value of this Decimal is zero or minus zero, otherwise
      returns <code>false</code>.
    </p>
    <pre>
x = new Decimal(0)
x.isZero()                               // true</pre>
    <p>Note: <code>n == 0</code> can be used if <code>n &gt;= Number.MIN_VALUE</code>.</p>



    <h5 id="lt">lessThan<code class='inset'>.lt(x) <i>&rArr; boolean</i></code></h5>
    <p><code>x</code>: <i>number|string|Decimal</i></p>
    <p>
      Returns <code>true</code> if the value of this Decimal is less than the value of
      <code>x</code>, otherwise returns <code>false</code>.
    </p>
    <p>Note: This method uses the <code>cmp</code> method internally.</p>
    <pre>
(0.3 - 0.2) &lt; 0.1                        // true
x = new Decimal(0.3).minus(0.2)
x.lessThan(0.1)                          // false
new Decimal(0).lt(x)                     // true</pre>



    <h5 id="lte">lessThanOrEqualTo<code class='inset'>.lte(x) <i>&rArr; boolean</i></code></h5>
    <p><code>x</code>: <i>number|string|Decimal</i></p>
    <p>
      Returns <code>true</code> if the value of this Decimal is less than or equal to the value of
      <code>x</code>, otherwise returns <code>false</code>.
    </p>
    <p>Note: This method uses the <code>cmp</code> method internally.</p>
    <pre>
0.1 &lt;= (0.3 - 0.2)                              // false
x = new Decimal(0.1)
x.lessThanOrEqualTo(Decimal(0.3).minus(0.2))    // true
new Decimal(-1).lte(x)                          // true</pre>



    <h5 id="log">logarithm<code class='inset'>.log(x) <i>&rArr; Decimal</i></code></h5>
    <p><code>x</code>: <i>number|string|Decimal</i></p>
    <p>
      Returns a new Decimal whose value is the base <code>x</code> logarithm of the value of this
      Decimal, truncated to <a href='#precision'><code>precision</code></a> significant digits.
    </p>
    <p>
      If <code>x</code> is omitted, the base 10 logarithm of the value of this Decimal will be
      returned.
    </p>
    <pre>
x = new Decimal(1000)
x.logarithm()                            // '3'
y = new Decimal(256)
y.log(2)                                 // '8'</pre>
    <p>The maximum error will be <code>1</code> <i>ulp</i> (unit in the last place).</p>
    <p>Logarithms to base <code>2</code> or <code>10</code> will always be correct.</p>
    <p>The performance of this method degrades exponentially with increasing digits.</p>



    <h5 id="sub">minus<code class='inset'>.minus(x) <i>&rArr; Decimal</i></code></h5>
    <p><code>x</code>: <i>number|string|Decimal</i></p>
    <p>
      Returns a new Decimal whose value is the value of this Decimal minus <code>x</code>, truncated
      to <a href='#precision'><code>precision</code></a> significant digits.
    </p>
    <pre>
0.3 - 0.1                                // 0.19999999999999998
x = new Decimal(0.3)
x.minus(0.1)                             // '0.2'</pre>



    <h5 id="mod">modulo<code class='inset'>.mod(x) <i>&rArr; Decimal</i></code></h5>
    <p><code>x</code>: <i>number|string|Decimal</i></p>
    <p>
      Returns a new Decimal whose value is the value of this Decimal modulo <code>x</code>,
      truncated to <a href='#precision'><code>precision</code></a> significant digits.
    </p>
    <pre>
1 % 0.9                                  // 0.09999999999999998
x = new Decimal(1)
y = x.modulo(0.9)                            // '0.1'</pre>



    <h5 id="exp">naturalExponential<code class='inset'>.exp() <i>&rArr; Decimal</i></code></h5>
    <p>
      Returns a new Decimal whose value is the base <code>e</code> (Euler's number, the base of the
      natural logarithm) exponential of the value of this Decimal, truncated to
      <a href='#precision'><code>precision</code></a> significant digits.
    </p>
    <p>
      The <code><a href='#ln'>naturalLogarithm</a></code> function is the inverse of this function.
    </p>
    <pre>
x = new Decimal(1)
x.naturalExponential()                   // '2.7182818284590452354'
y = new Decimal(2)
y.exp()                                  // '7.3890560989306502272'</pre>
    <p>The maximum error will be <code>1</code> <i>ulp</i> (unit in the last place).</p>
    <p>The performance of this method degrades exponentially with increasing digits.</p>



    <h5 id="ln">naturalLogarithm<code class='inset'>.ln() <i>&rArr; Decimal</i></code></h5>
    <p>
      Returns a new Decimal whose value is the natural logarithm of the value of this Decimal,
      truncated to <a href='#precision'><code>precision</code></a> significant digits.
    </p>
    <p>
      The natural logarithm is the inverse of the <code><a href='#exp'>naturalExponential</a></code>
      function.
    </p>
    <pre>
x = new Decimal(10)
x.naturalLogarithm()                     // '2.3026'
y = new Decimal('1.23e+30')
y.ln()                                   // '69.28'</pre>
    <p>
      The mathematical result of the natural logarithm function is non-terminating, unless its
      argument is <code>1</code>.
    </p>
    <p>
      The time-taken by this method increases exponentially with increasing digits.
    </p>
    <p>
      See <a href='#ln10'>LN10</a> to configure the maximum precision available.
    </p>



    <h5 id="neg">negated<code class='inset'>.neg() <i>&rArr; Decimal</i></code></h5>
    <p>
      Returns a new Decimal whose value is the value of this Decimal negated, i.e. multiplied by
      <code>-1</code>.
    </p>
    <p>
      The return value is not affected by the value of the
      <a href='#precision'><code>precision</code></a> setting.
    </p>
    <pre>
x = new Decimal(1.8)
x.negated()                              // '-1.8'
y = new Decimal(-1.3)
y.neg()                                  // '1.3'</pre>



    <h5 id="add">plus<code class='inset'>.plus(x) <i>&rArr; Decimal</i></code></h5>
    <p><code>x</code>: <i>number|string|Decimal</i></p>
    <p>
      Returns a new Decimal whose value is the value of this Decimal plus <code>x</code>, truncated
      to <a href='#precision'><code>precision</code></a> significant digits.
    </p>
    <pre>
0.1 + 0.2                                // 0.30000000000000004
x = new Decimal(0.1)
y = x.plus(0.2)                          // '0.3'
new Decimal(0.7).plus(x).plus(y)         // '1.1'</pre>



    <h5 id="sd">precision<code class='inset'>.sd([include_zeros]) <i>&rArr; number</i></code></h5>
    <p>Returns the number of significant digits of the value of this Decimal.</p>
    <p>
      If <code>include_zeros</code> is <code>true</code> or <code>1</code> then any trailing zeros
      of the integer part of a number are counted as significant digits, otherwise they are not.
    </p>
    <pre>
x = new Decimal(1.234)
x.precision()                            // '4'
y = new Decimal(987000)
y.sd()                                   // '3'
y.sd(true)                               // '6'</pre>



    <h5 id="sqrt">squareRoot<code class='inset'>.sqrt() <i>&rArr; Decimal</i></code></h5>
    <p>
      Returns a new Decimal whose value is the square root of this Decimal, truncated to
      <a href='#precision'><code>precision</code></a> significant digits.
    </p>
    <p>
      This method is much faster than using the <a href='#pow'><code>toPower</code></a> method with
      an exponent of <code>0.5</code>.
    </p>
    <pre>
x = new Decimal(16)
x.squareRoot()                           // '4'
y = new Decimal(3)
y.sqrt()                                 // '1.73205080756887729353'
y.sqrt().eq( y.pow(0.5) )                // true</pre>



    <h5 id="mul">times<code class='inset'>.times(x) <i>&rArr; Decimal</i></code></h5>
    <p><code>x</code>: <i>number|string|Decimal</i></p>
    <p>
      Returns a new Decimal whose value is the value of this Decimal times <code>x</code>,
      truncated to <a href='#precision'><code>precision</code></a> significant digits.
    </p>
    <pre>
0.6 * 3                                  // 1.7999999999999998
x = new Decimal(0.6)
y = x.times(3)                           // '1.8'
new Decimal('7e+500').times(y)           // '1.26e+501'</pre>



    <h5 id="todp">
      toDecimalPlaces<code class='inset'>.todp([dp [, rm]]) <i>&rArr; Decimal</i></code>
    </h5>
    <p>
      <code>dp</code>: <i>number</i>: integer, <code>0</code> to <code>1e+9</code> inclusive<br />
      <code>rm</code>: <i>number</i>: integer, <code>0</code> to <code>8</code> inclusive.
    </p>
    <p>
      Returns a new Decimal whose value is the value of this Decimal rounded to a maximum of
      <code>dp</code> decimal places using rounding mode <code>rm</code>.
    </p>
    <p>
      If <code>dp</code> is omitted, the return value will have the same value as this Decimal.
    </p>
    <p>
      If <code>rm</code> is omitted, rounding mode <a href='#rounding'><code>rounding</code></a>
      is used.
    </p>
    <p>Throws on an invalid <code>dp</code> or <code>rm</code> value.</p>
    <pre>
x = new Decimal(12.24567)
x.toDecimalPlaces(0)                // '12'
x.toDecimalPlaces(1, 0)             // '12.3'

y = new Decimal(9876.54321)
y.todp(3)                           // '9876.543'
y.todp(1, 0)                        // '9876.6'
y.todp(1, Decimal.ROUND_DOWN)       // '9876.5'</pre>



    <h5 id="toExponential">
      toExponential<code class='inset'>.toExponential([dp [, rm]]) <i>&rArr; string</i></code>
    </h5>
    <p>
      <code>dp</code>: <i>number</i>: integer, <code>0</code> to <code>1e+9</code> inclusive<br />
      <code>rm</code>: <i>number</i>: integer, <code>0</code> to <code>8</code> inclusive
    </p>
    <p>
      Returns a string representing the value of this Decimal in exponential notation rounded
      using rounding mode <code>rm</code> to <code>dp</code> decimal places, i.e with one digit
      before the decimal point and <code>dp</code> digits after it.
    </p>
    <p>
      If the value of this Decimal in exponential notation has fewer than <code>dp</code> fraction
      digits, the return value will be appended with zeros accordingly.
    </p>
    <p>
      If <code>dp</code> is omitted, the number of digits after the decimal point defaults to the
      minimum number of digits necessary to represent the value exactly.
    </p>
    <p>
      If <code>rm</code> is omitted, rounding mode <a href='#rounding'><code>rounding</code></a> is
      used.
    </p>
    <p>Throws on an invalid <code>dp</code> or <code>rm</code> value.</p>
    <pre>
x = 45.6
b = new Decimal(x)
x.toExponential()              // '4.56e+1'
y.toExponential()              // '4.56e+1'
x.toExponential(0)             // '5e+1'
y.toExponential(0)             // '5e+1'
x.toExponential(1)             // '4.6e+1'
y.toExponential(1)             // '4.6e+1'
y.toExponential(1, 1)          // '4.5e+1'  (ROUND_DOWN)
x.toExponential(3)             // '4.560e+1'
y.toExponential(3)             // '4.560e+1'</pre>



    <h5 id="toFixed">
      toFixed<code class='inset'>.toFixed([dp [, rm]]) <i>&rArr; string</i></code>
    </h5>
    <p>
      <code>dp</code>: <i>number</i>: integer, <code>0</code> to <code>1e+9</code> inclusive<br />
      <code>rm</code>: <i>number</i>: integer, <code>0</code> to <code>8</code> inclusive
    </p>
    <p>
      Returns a string representing the value of this Decimal in normal (fixed-point) notation
      rounded to <code>dp</code> decimal places using rounding mode <code>rm</code>.
    </p>
    <p>
      If the value of this Decimal in normal notation has fewer than <code>dp</code> fraction
      digits, the return value will be appended with zeros accordingly.
    </p>
    <p>
      Unlike <code>Number.prototype.toFixed</code>, which returns exponential notation if a number
      is greater or equal to <code>10<sup>21</sup></code>, this method will always return normal
      notation.
    </p>
    <p>
      If <code>dp</code> is omitted, the return value will be unrounded and in normal notation. This
      is unlike <code>Number.prototype.toFixed</code>, which returns the value to zero decimal
      places, but is useful when because of the current
      <a href="#toExpNeg"><code>toExpNeg</code></a> or
      <a href="#toExpPos"><code>toExpNeg</code></a> values,
      <code><a href='#toString'>toString</a></code> returns exponential notation.
    </p>
    <p>
      If <code>rm</code> is omitted, rounding mode <a href='#rounding'><code>rounding</code></a> is
      used.
    </p>
    <p>Throws on an invalid <code>dp</code> or <code>rm</code> value.</p>
    <pre>
x = 3.456
b = new Decimal(x)
x.toFixed()              // '3'
y.toFixed()              // '3.456'
y.toFixed(0)             // '3'
x.toFixed(2)             // '3.46'
y.toFixed(2)             // '3.46'
y.toFixed(2, 1)          // '3.45'  (ROUND_DOWN)
x.toFixed(5)             // '3.45600'
y.toFixed(5)             // '3.45600'</pre>



    <h5 id="toInteger">toInteger<code class='inset'>.toint() <i>&rArr; Decimal</i></code></h5>
    <p>
      Returns a new Decimal whose value is the value of this Decimal rounded to a whole number using
      rounding mode <a href='#rounding'><code>rounding</code></a>.
    </p>
    <p>
      To emulate <code>Math.round</code>, set <a href='#rounding'><code>rounding</code></a> to
      <code>7</code>, i.e. <a href='#modes'><code>ROUND_HALF_CEIL</code></a>.
    </p>
    <pre>
Decimal.config({ rounding: 4 })
x = 1234.5
x.toInteger()                            // '1235'

Decimal.rounding = Decimal.ROUND_DOWN
x.toint()                                // '1234'
x                                        // '1234.5'</pre>



<h5 id="toJSON">toJSON<code class='inset'>.toJSON() <i>&rArr; string</i></code></h5>
    <p>As <a href='#toString'><code>toString</code></a>.</p>



    <h5 id="toNumber">toNumber<code class='inset'>.toNumber() <i>&rArr; number</i></code></h5>
    <p>Returns the value of this Decimal converted to a primitive number.</p>
    <p>
      Type coercion with, for example, JavaScript's unary plus operator will also work, except that
      a Decimal with the value minus zero will convert to positive zero.
    </p>
    <pre>
x = new Decimal(456.789)
x.toNumber()                   // 456.789
+x                             // 456.789

y = new Decimal('45987349857634085409857349856430985')
y.toNumber()                   // 4.598734985763409e+34</pre>



    <h5 id="pow">toPower<code class='inset'>.pow(x) <i>&rArr; Decimal</i></code></h5>
    <p><code>x</code>: <i>number|string|Decimal</i>: integer or non-integer</p>
    <p>
      Returns a new Decimal whose value is the value of this Decimal raised to the power
      <code>x</code>, truncated to <a href='#precision'><code>precision</code></a> significant
      digits.
    </p>
    <p>
      The performance of this method degrades exponentially with increasing digits.<br />
      For non-integer exponents in particular, the performance of this method may not be adequate.
    </p>
    <p>The maximum error will be <code>1</code> <i>ulp</i> (unit in the last place). </p>
    <pre>
Math.pow(0.7, 2)               // 0.48999999999999994
x = new Decimal(0.7)
x.toPower(2)                   // '0.49'
new Decimal(3).pow(-2)         // '0.11111111111111111111'

new Decimal(1217652.23).pow('98765.489305603941')
// '4.8227010515242461181e+601039'</pre>




    <h5 id="toPrecision">
      toPrecision<code class='inset'>.toPrecision([sd [, rm]]) <i>&rArr; string</i></code>
    </h5>
    <p>
      <code>sd</code>: <i>number</i>: integer, <code>1</code> to <code>1e+9</code> inclusive<br />
      <code>rm</code>: <i>number</i>: integer, <code>0</code> to <code>8</code> inclusive
    </p>
    <p>
      Returns a string representing the value of this Decimal rounded to <code>sd</code> significant
      digits using rounding mode <code>rm</code>.
    </p>
    <p>
      If <code>sd</code> is less than the number of digits necessary to represent the integer part
      of the value in normal (fixed-point) notation, then exponential notation is used.
    </p>
    <p>
      If <code>sd</code> is omitted, the return value is the same as
      <code><a href='#toString'>toString</a></code>.
    </p>
    <p>
      If <code>rm</code> is omitted, rounding mode <a href='#rounding'><code>rounding</code></a> is
      used.
    </p>
    <p>Throws on an invalid <code>sd</code> or <code>rm</code> value.</p>
    <pre>
x = 45.6
b = new Decimal(x)
x.toPrecision()                          // '45.6'
y.toPrecision()                          // '45.6'
x.toPrecision(1)                         // '5e+1'
y.toPrecision(1)                         // '5e+1'
y.toPrecision(2, 0)                      // '4.6e+1'  (ROUND_UP)
y.toPrecision(2, 1)                      // '4.5e+1'  (ROUND_DOWN)
x.toPrecision(5)                         // '45.600'
y.toPrecision(5)                         // '45.600'</pre>



    <h5 id="tosd">
      toSignificantDigits<code class='inset'>.tosd([sd [, rm]]) <i>&rArr; Decimal</i></code>
    </h5>
    <p>
      <code>sd</code>: <i>number</i>: integer, <code>1</code> to <code>1e+9</code> inclusive.<br />
      <code>rm</code>: <i>number</i>: integer, <code>0</code> to <code>8</code> inclusive.
    </p>
    <p>
      Returns a new Decimal whose value is the value of this Decimal rounded to a maximum of
      <code>sd</code> significant digits using rounding mode <code>rm</code>.
    </p>
    <p>
      If <code>sd</code> is omitted, the return value will be rounded to
      <a href='#precision'><code>precision</code></a> significant digits.
    </p>
    <p>
      If <code>rm</code> is omitted, rounding mode <a href='#rounding'><code>rounding</code></a>
      will be used.
    </p>
    <p>Throws on an invalid <code>sd</code> or <code>rm</code> value.</p>
    <pre>
Decimal.config({ precision: 5, rounding: 4 })
x = new Decimal(9876.54321)

x.toSignificantDigits()                          // '9876.5'
x.toSignificantDigits(6)                         // '9876.54'
x.toSignificantDigits(6, Decimal.ROUND_UP)       // '9876.55'
x.tosd(2)                                        // '9900'
x.tosd(2, 1)                                     // '9800'
x                                                // '9876.54321'</pre>



    <h5 id="toString">toString<code class='inset'>.toString() <i>&rArr; string</i></code></h5>
    <p>Returns a string representing the value of this Decimal.</p>
    <p>
      If this Decimal has a positive exponent that is equal to or greater than
      <a href="#toExpPos"><code>toExpPos</code></a>, or a negative exponent equal to or less than
      <a href="#toExpPos"><code>toExpNeg</code></a>, then exponential notation will be returned.
    </p>
    <pre>
x = new Decimal(750000)
x.toString()                             // '750000'
Decimal.config({ toExpPos: 5 })
x.toString()                             // '7.5e+5'

Decimal.config({ precision: 4 });
y = new Decimal('1.23456789')
y.toString()                             // '1.23456789'</pre>



    <h5 id="valueOf">valueOf<code class='inset'>.val() <i>&rArr; string</i></code></h5>
    <p>As <a href='#toString'><code>toString</code></a>.</p>





    <h4 id="instance-properties">Properties</h4>
    <p>
      The value of a Decimal is stored in a normalised base <code>10000000</code> floating point
      format.
    </p>
    <p>
      A Decimal instance is an object with three properties:
    </p>
    <table>
      <tr>
        <th>Property</th>
        <th>Description</th>
        <th>Type</th>
        <th>Value</th>
      </tr>
      <tr>
        <td class='centre' id='digits'><b>d</b></td>
        <td>digits</td>
        <td><i>number</i><code style='color:#000'>[]</code></td>
        <td> Array of integers, each <code>0</code> - <code>1e7</code></td>
      </tr>
      <tr>
        <td class='centre' id='exponent'><b>e</b></td>
        <td>exponent*</td>
        <td><i>number</i></td>
        <td>Integer, <code>-1286742750677284</code> to <code>1286742750677284</code> inclusive</td>
      </tr>
      <tr>
        <td class='centre' id='sign'><b>s</b></td>
        <td>sign</td>
        <td><i>number</i></td>
        <td><code>-1</code>, <code>0</code>, or <code>1</code></td>
      </tr>
    </table>
     <p>
      *This is the exponent in base <code>10000000</code>. To get the base 10 exponent, use the
      <a href='#exp'><code>exponent</code></a> method.
    </p>
    <p>The properties are best considered to be read-only.</p>
    <p>
      As with JavaScript numbers, the original exponent and fractional trailing zeros of a number
      are not preserved.
    </p>
    <pre>
x = new Decimal(0.123)                   // '0.123'
x.toExponential()                        // '1.23e-1'
x.d                                      // [ 1230000 ]
x.e                                      // -1
x.s                                      // 1

y = new Number(-123.4567000e+2)          // '-12345.67'
y.toExponential()                        // '-1.234567e+4'
z = new Decimal('-123.4567000e+2')       // '-12345.67'
z.toExponential()                        // '-1.234567e+4'
z.d                                      // [ 12345, 6700000 ]
z.e                                      // 4
z.s                                      // -1</pre>



    <h4 id='Errors'>Errors</h4>
    <p>
      The errors that are thrown are generic <code>Error</code> objects whose <code>message</code>
      property begins with <code>"[DecimalError]"</code>.
    </p>
    <p>To determine if an exception is a Decimal Error:</p>
    <pre>
try {
    // ...
} catch (e) {
    if ( e instanceof Error && /DecimalError/.test(e.message) ) {
        // ...
    }
}</pre>



    <h2 id='faq'>FAQ</h2>
    <h6>Why are trailing fractional zeros removed from Decimals?</h6>
    <p>
      Some arbitrary-precision libraries retain trailing fractional zeros as they can indicate the
      precision of a value. This can be useful but the results of arithmetic operations can be
      misleading.
    </p>
    <pre>
x = new BigDecimal("1.0")
y = new BigDecimal("1.1000")
z = x.add(y)                      // 2.1000

x = new BigDecimal("1.20")
y = new BigDecimal("3.45000")
z = x.multiply(y)                 // 4.1400000</pre>
    <p>
      To specify the precision of a value is to specify that the value lies
      within a certain range.
    </p>
    <p>
      In the first example, <code>x</code> has a value of <code>1.0</code>. The trailing zero shows
      the precision of the value, implying that it is in the range <code>0.95</code> to
      <code>1.05</code>. Similarly, the precision indicated by the trailing zeros of <code>y</code>
      indicates that the value is in the range <code>1.09995</code> to <code>1.10005</code>.
    </p>
    <p>
      If we  add the two lowest values in the ranges we have, <code>0.95 + 1.09995 = 2.04995</code>,
      and if we add the two highest values we have, <code>1.05 + 1.10005 = 2.15005</code>, so the
      range of the result of the addition implied by the precision of its operands is
      <code>2.04995</code> to <code>2.15005</code>.
    </p>
    <p>
      The result given by BigDecimal of <code>2.1000</code> however, indicates that the value is in
      the range <code>2.09995</code> to <code>2.10005</code> and therefore the precision implied by
      its trailing zeros may be misleading.
    </p>
    <p>
      In the second example, the true range is <code>4.122744</code> to <code>4.157256</code> yet
      the BigDecimal answer of <code>4.1400000</code> indicates a range of <code>4.13999995</code>
      to  <code>4.14000005</code>. Again, the precision implied by the trailing zeros may be
      misleading.
    </p>
    <p>
      This library, like binary floating point and most calculators, does not retain trailing
      fractional zeros. Instead, the <code>toExponential</code>, <code>toFixed</code> and
      <code>toPrecision</code> methods enable trailing zeros to be added if and when required.<br />
    </p>
  </div>

</body>
</html>
