<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vehicles', function (Blueprint $table) {
            $table->id();
            $table->string('registration_number', 20)->unique();
            $table->string('make', 50);
            $table->string('model', 50);
            $table->integer('year');
            $table->string('vin', 17)->unique()->nullable();
            $table->enum('type', ['car', 'truck', 'van', 'motorcycle', 'equipment']);
            $table->enum('status', ['active', 'maintenance', 'out_of_service', 'sold'])->default('active');
            $table->date('purchase_date')->nullable();
            $table->decimal('purchase_price', 12, 2)->nullable();
            $table->integer('current_mileage')->default(0);
            $table->enum('fuel_type', ['gasoline', 'diesel', 'electric', 'hybrid']);
            $table->string('capacity', 50)->nullable(); // Passenger or cargo capacity
            $table->timestamps();

            // Indexes
            $table->index('registration_number');
            $table->index('make');
            $table->index('model');
            $table->index('type');
            $table->index('status');
            $table->index('fuel_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vehicles');
    }
};
