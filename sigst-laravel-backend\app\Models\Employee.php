<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

class Employee extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'employee_id',
        'first_name',
        'last_name',
        'email',
        'phone',
        'position',
        'department',
        'hire_date',
        'status',
        'emergency_contact_name',
        'emergency_contact_phone',
        'address',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'hire_date' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($employee) {
            if (empty($employee->employee_id)) {
                $employee->employee_id = self::generateEmployeeId();
            }
        });
    }

    /**
     * Generate a unique employee ID.
     */
    private static function generateEmployeeId(): string
    {
        $prefix = 'EMP';
        $lastEmployee = self::where('employee_id', 'like', $prefix . '%')
            ->orderBy('employee_id', 'desc')
            ->first();

        if ($lastEmployee) {
            $lastNumber = (int) substr($lastEmployee->employee_id, strlen($prefix));
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get the full name attribute.
     */
    public function getFullNameAttribute(): string
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    /**
     * Get the employee's skills.
     */
    public function skills(): BelongsToMany
    {
        return $this->belongsToMany(Skill::class, 'employee_skills')
            ->withPivot([
                'proficiency_level',
                'acquired_date',
                'expiry_date'
            ])
            ->withTimestamps();
    }

    /**
     * Get the employee's certifications.
     */
    public function certifications(): BelongsToMany
    {
        return $this->belongsToMany(Certification::class, 'employee_certifications')
            ->withPivot([
                'issue_date',
                'expiry_date',
                'certificate_number',
                'status'
            ])
            ->withTimestamps();
    }

    /**
     * Get the employee's trainings.
     */
    public function trainings(): BelongsToMany
    {
        return $this->belongsToMany(Training::class, 'employee_trainings')
            ->withPivot([
                'enrollment_date',
                'completion_date',
                'status',
                'score',
                'instructor'
            ])
            ->withTimestamps();
    }

    /**
     * Get the employee's vehicle interventions.
     */
    public function vehicleInterventions(): HasMany
    {
        return $this->hasMany(VehicleIntervention::class, 'technician_id');
    }

    /**
     * Get the employee's vehicle accidents as driver.
     */
    public function vehicleAccidents(): HasMany
    {
        return $this->hasMany(VehicleAccident::class, 'driver_id');
    }

    /**
     * Get the employee's maintenance requests.
     */
    public function maintenanceRequests(): HasMany
    {
        return $this->hasMany(MaintenanceRequest::class, 'requester_id');
    }

    /**
     * Get the employee's assigned maintenance requests.
     */
    public function assignedMaintenanceRequests(): HasMany
    {
        return $this->hasMany(MaintenanceRequest::class, 'assigned_technician_id');
    }

    /**
     * Get the employee's maintenance operations.
     */
    public function maintenanceOperations(): HasMany
    {
        return $this->hasMany(MaintenanceOperation::class, 'technician_id');
    }

    /**
     * Get the employee's tool assignments.
     */
    public function toolAssignments(): HasMany
    {
        return $this->hasMany(ToolAssignment::class);
    }

    /**
     * Get the employee's assigned tools.
     */
    public function assignedTools(): HasMany
    {
        return $this->hasMany(Tool::class, 'assigned_to_employee_id');
    }

    /**
     * Get the employee's product consumption records.
     */
    public function productConsumptions(): HasMany
    {
        return $this->hasMany(ProductConsumption::class, 'consumed_by_employee_id');
    }

    /**
     * Get the employee's fuel transactions as driver.
     */
    public function fuelTransactions(): HasMany
    {
        return $this->hasMany(FuelTransaction::class, 'driver_id');
    }

    /**
     * Get the employee's parts movements.
     */
    public function partsMovements(): HasMany
    {
        return $this->hasMany(PartsMovement::class);
    }

    /**
     * Get the user account for this employee.
     */
    public function user(): HasOne
    {
        return $this->hasOne(User::class);
    }

    /**
     * Scope a query to only include active employees.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to search employees by name or email.
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('first_name', 'ilike', "%{$search}%")
              ->orWhere('last_name', 'ilike', "%{$search}%")
              ->orWhere('email', 'ilike', "%{$search}%")
              ->orWhere('employee_id', 'ilike', "%{$search}%");
        });
    }

    /**
     * Scope a query to filter by department.
     */
    public function scopeByDepartment($query, $department)
    {
        return $query->where('department', $department);
    }

    /**
     * Scope a query to filter by position.
     */
    public function scopeByPosition($query, $position)
    {
        return $query->where('position', $position);
    }

    /**
     * Get employees with expiring certifications.
     */
    public function scopeWithExpiringCertifications($query, $days = 30)
    {
        return $query->whereHas('certifications', function ($q) use ($days) {
            $q->where('employee_certifications.expiry_date', '<=', now()->addDays($days))
              ->where('employee_certifications.status', 'active');
        });
    }
}
