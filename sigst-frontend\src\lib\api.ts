import axios from 'axios';
import { toast } from 'react-hot-toast';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user');
      window.location.href = '/login';
      toast.error('Session expired. Please login again.');
    } else if (error.response?.status === 403) {
      toast.error('You do not have permission to perform this action.');
    } else if (error.response?.status === 422) {
      // Validation errors
      const errors = error.response.data.errors;
      if (errors) {
        Object.values(errors).flat().forEach((message: any) => {
          toast.error(message);
        });
      } else {
        toast.error(error.response.data.message || 'Validation error occurred.');
      }
    } else if (error.response?.status >= 500) {
      toast.error('Server error occurred. Please try again later.');
    } else {
      toast.error(error.response?.data?.message || 'An error occurred.');
    }
    
    return Promise.reject(error);
  }
);

export default api;

// API response types
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  meta?: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
  };
  links: {
    first: string;
    last: string;
    prev: string | null;
    next: string | null;
  };
}

// Generic API functions
export const apiRequest = {
  get: <T>(url: string, params?: any) => 
    api.get<ApiResponse<T>>(url, { params }).then(res => res.data),
  
  post: <T>(url: string, data?: any) => 
    api.post<ApiResponse<T>>(url, data).then(res => res.data),
  
  put: <T>(url: string, data?: any) => 
    api.put<ApiResponse<T>>(url, data).then(res => res.data),
  
  patch: <T>(url: string, data?: any) => 
    api.patch<ApiResponse<T>>(url, data).then(res => res.data),
  
  delete: <T>(url: string) => 
    api.delete<ApiResponse<T>>(url).then(res => res.data),
};

export { api };
