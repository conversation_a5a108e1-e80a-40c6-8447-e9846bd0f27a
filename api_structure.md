# SIGST API Structure Documentation

## API Architecture Overview

### Base Configuration
- **Framework**: Laravel 11.x
- **API Version**: v1
- **Base URL**: `/api/v1`
- **Authentication**: JWT (JSON Web Tokens)
- **Response Format**: JSON
- **Error Handling**: Standardized HTTP status codes with descriptive messages

### Authentication Endpoints

```
POST   /api/v1/auth/login
POST   /api/v1/auth/logout
POST   /api/v1/auth/refresh
GET    /api/v1/auth/me
POST   /api/v1/auth/change-password
```

## Module API Endpoints

### 1. Personnel Management Module

#### Employees
```
GET    /api/v1/employees                    # List all employees with pagination and search
POST   /api/v1/employees                    # Create new employee
GET    /api/v1/employees/{id}               # Get employee details
PUT    /api/v1/employees/{id}               # Update employee
DELETE /api/v1/employees/{id}               # Delete employee
GET    /api/v1/employees/search             # Search employees by keywords
GET    /api/v1/employees/{id}/skills        # Get employee skills
GET    /api/v1/employees/{id}/certifications # Get employee certifications
GET    /api/v1/employees/{id}/trainings     # Get employee trainings
```

#### Skills
```
GET    /api/v1/skills                       # List all skills
POST   /api/v1/skills                       # Create new skill
GET    /api/v1/skills/{id}                  # Get skill details
PUT    /api/v1/skills/{id}                  # Update skill
DELETE /api/v1/skills/{id}                  # Delete skill
POST   /api/v1/employees/{employeeId}/skills/{skillId} # Assign skill to employee
DELETE /api/v1/employees/{employeeId}/skills/{skillId} # Remove skill from employee
```

#### Certifications
```
GET    /api/v1/certifications               # List all certifications
POST   /api/v1/certifications               # Create new certification
GET    /api/v1/certifications/{id}          # Get certification details
PUT    /api/v1/certifications/{id}          # Update certification
DELETE /api/v1/certifications/{id}          # Delete certification
GET    /api/v1/certifications/expiring      # Get expiring certifications
POST   /api/v1/employees/{employeeId}/certifications # Assign certification
PUT    /api/v1/employees/{employeeId}/certifications/{certId} # Update employee certification
```

#### Trainings
```
GET    /api/v1/trainings                    # List all trainings
POST   /api/v1/trainings                    # Create new training
GET    /api/v1/trainings/{id}               # Get training details
PUT    /api/v1/trainings/{id}               # Update training
DELETE /api/v1/trainings/{id}               # Delete training
POST   /api/v1/trainings/{id}/enroll        # Enroll employees in training
PUT    /api/v1/trainings/{id}/complete      # Mark training as completed
```

### 2. Vehicle Management Module

#### Vehicles
```
GET    /api/v1/vehicles                     # List all vehicles with filters
POST   /api/v1/vehicles                     # Create new vehicle
GET    /api/v1/vehicles/{id}                # Get vehicle details
PUT    /api/v1/vehicles/{id}                # Update vehicle
DELETE /api/v1/vehicles/{id}                # Delete vehicle
GET    /api/v1/vehicles/search              # Search vehicles by keywords
GET    /api/v1/vehicles/{id}/documents      # Get vehicle documents
GET    /api/v1/vehicles/{id}/interventions  # Get vehicle intervention history
GET    /api/v1/vehicles/{id}/accidents      # Get vehicle accident history
GET    /api/v1/vehicles/maintenance-due     # Get vehicles due for maintenance
```

#### Vehicle Documents
```
GET    /api/v1/vehicle-documents            # List all documents
POST   /api/v1/vehicle-documents            # Create new document
GET    /api/v1/vehicle-documents/{id}       # Get document details
PUT    /api/v1/vehicle-documents/{id}       # Update document
DELETE /api/v1/vehicle-documents/{id}       # Delete document
POST   /api/v1/vehicle-documents/{id}/upload # Upload document file
GET    /api/v1/vehicle-documents/expiring   # Get expiring documents
```

#### Vehicle Interventions
```
GET    /api/v1/vehicle-interventions        # List all interventions
POST   /api/v1/vehicle-interventions        # Create new intervention
GET    /api/v1/vehicle-interventions/{id}   # Get intervention details
PUT    /api/v1/vehicle-interventions/{id}   # Update intervention
DELETE /api/v1/vehicle-interventions/{id}   # Delete intervention
```

#### Vehicle Accidents
```
GET    /api/v1/vehicle-accidents            # List all accidents
POST   /api/v1/vehicle-accidents            # Create new accident report
GET    /api/v1/vehicle-accidents/{id}       # Get accident details
PUT    /api/v1/vehicle-accidents/{id}       # Update accident report
DELETE /api/v1/vehicle-accidents/{id}       # Delete accident report
```

### 3. Maintenance Management Module

#### Maintenance Schedules
```
GET    /api/v1/maintenance-schedules        # List all maintenance schedules
POST   /api/v1/maintenance-schedules        # Create new schedule
GET    /api/v1/maintenance-schedules/{id}   # Get schedule details
PUT    /api/v1/maintenance-schedules/{id}   # Update schedule
DELETE /api/v1/maintenance-schedules/{id}   # Delete schedule
GET    /api/v1/maintenance-schedules/due    # Get overdue maintenance
POST   /api/v1/maintenance-schedules/bulk   # Bulk create schedules
```

#### Maintenance Requests
```
GET    /api/v1/maintenance-requests         # List all requests with filters
POST   /api/v1/maintenance-requests         # Create new request
GET    /api/v1/maintenance-requests/{id}    # Get request details
PUT    /api/v1/maintenance-requests/{id}    # Update request
DELETE /api/v1/maintenance-requests/{id}    # Delete request
POST   /api/v1/maintenance-requests/{id}/approve # Approve request
POST   /api/v1/maintenance-requests/{id}/assign  # Assign technician
POST   /api/v1/maintenance-requests/{id}/complete # Complete request
GET    /api/v1/maintenance-requests/my      # Get current user's requests
```

#### Maintenance Operations
```
GET    /api/v1/maintenance-operations       # List all operations
POST   /api/v1/maintenance-operations       # Create new operation
GET    /api/v1/maintenance-operations/{id}  # Get operation details
PUT    /api/v1/maintenance-operations/{id}  # Update operation
DELETE /api/v1/maintenance-operations/{id}  # Delete operation
POST   /api/v1/maintenance-operations/{id}/quality-check # Quality check
```

### 4. Spare Parts Management Module

#### Spare Parts
```
GET    /api/v1/spare-parts                  # List all spare parts
POST   /api/v1/spare-parts                  # Create new spare part
GET    /api/v1/spare-parts/{id}             # Get spare part details
PUT    /api/v1/spare-parts/{id}             # Update spare part
DELETE /api/v1/spare-parts/{id}             # Delete spare part
GET    /api/v1/spare-parts/search           # Search spare parts
GET    /api/v1/spare-parts/low-stock        # Get low stock items
GET    /api/v1/spare-parts/{id}/movements   # Get part movement history
```

#### Parts Inventory
```
GET    /api/v1/parts-inventory              # List inventory status
PUT    /api/v1/parts-inventory/{id}         # Update inventory
GET    /api/v1/parts-inventory/alerts       # Get stock alerts
POST   /api/v1/parts-inventory/adjustment   # Stock adjustment
```

#### Parts Movements
```
GET    /api/v1/parts-movements              # List all movements
POST   /api/v1/parts-movements              # Create new movement
GET    /api/v1/parts-movements/{id}         # Get movement details
PUT    /api/v1/parts-movements/{id}         # Update movement
DELETE /api/v1/parts-movements/{id}         # Delete movement
POST   /api/v1/parts-movements/bulk         # Bulk movements
```

#### Parts Suppliers
```
GET    /api/v1/parts-suppliers              # List all suppliers
POST   /api/v1/parts-suppliers              # Create new supplier
GET    /api/v1/parts-suppliers/{id}         # Get supplier details
PUT    /api/v1/parts-suppliers/{id}         # Update supplier
DELETE /api/v1/parts-suppliers/{id}         # Delete supplier
GET    /api/v1/parts-suppliers/{id}/parts   # Get supplier parts
```

### 5. Tool Management Module

#### Tools
```
GET    /api/v1/tools                        # List all tools
POST   /api/v1/tools                        # Create new tool
GET    /api/v1/tools/{id}                   # Get tool details
PUT    /api/v1/tools/{id}                   # Update tool
DELETE /api/v1/tools/{id}                   # Delete tool
GET    /api/v1/tools/search                 # Search tools
GET    /api/v1/tools/available              # Get available tools
GET    /api/v1/tools/calibration-due        # Get tools due for calibration
GET    /api/v1/tools/{id}/assignments       # Get tool assignment history
```

#### Tool Assignments
```
GET    /api/v1/tool-assignments             # List all assignments
POST   /api/v1/tool-assignments             # Create new assignment
GET    /api/v1/tool-assignments/{id}        # Get assignment details
PUT    /api/v1/tool-assignments/{id}        # Update assignment
DELETE /api/v1/tool-assignments/{id}        # Delete assignment
POST   /api/v1/tool-assignments/{id}/return # Return tool
GET    /api/v1/tool-assignments/overdue     # Get overdue assignments
```

### 6. Product Management Module

#### Products
```
GET    /api/v1/products                     # List all products
POST   /api/v1/products                     # Create new product
GET    /api/v1/products/{id}                # Get product details
PUT    /api/v1/products/{id}                # Update product
DELETE /api/v1/products/{id}                # Delete product
GET    /api/v1/products/search              # Search products
GET    /api/v1/products/expiring            # Get expiring products
GET    /api/v1/products/{id}/inventory      # Get product inventory
GET    /api/v1/products/{id}/consumption    # Get consumption history
```

#### Product Inventory
```
GET    /api/v1/product-inventory            # List inventory status
POST   /api/v1/product-inventory            # Add inventory batch
GET    /api/v1/product-inventory/{id}       # Get inventory details
PUT    /api/v1/product-inventory/{id}       # Update inventory
DELETE /api/v1/product-inventory/{id}       # Delete inventory batch
GET    /api/v1/product-inventory/alerts     # Get expiry alerts
```

#### Product Consumption
```
GET    /api/v1/product-consumption          # List all consumption
POST   /api/v1/product-consumption          # Record consumption
GET    /api/v1/product-consumption/{id}     # Get consumption details
PUT    /api/v1/product-consumption/{id}     # Update consumption
DELETE /api/v1/product-consumption/{id}     # Delete consumption
GET    /api/v1/product-consumption/reports  # Get consumption reports
```

### 7. Fuel Management Module

#### Fuel Stations
```
GET    /api/v1/fuel-stations                # List all fuel stations
POST   /api/v1/fuel-stations                # Create new station
GET    /api/v1/fuel-stations/{id}           # Get station details
PUT    /api/v1/fuel-stations/{id}           # Update station
DELETE /api/v1/fuel-stations/{id}           # Delete station
```

#### Fuel Transactions
```
GET    /api/v1/fuel-transactions            # List all transactions
POST   /api/v1/fuel-transactions            # Create new transaction
GET    /api/v1/fuel-transactions/{id}       # Get transaction details
PUT    /api/v1/fuel-transactions/{id}       # Update transaction
DELETE /api/v1/fuel-transactions/{id}       # Delete transaction
GET    /api/v1/fuel-transactions/reports    # Get fuel consumption reports
GET    /api/v1/fuel-transactions/summary    # Get consumption summary
```

#### Fuel Efficiency
```
GET    /api/v1/fuel-efficiency              # List efficiency records
POST   /api/v1/fuel-efficiency              # Create efficiency record
GET    /api/v1/fuel-efficiency/{id}         # Get efficiency details
PUT    /api/v1/fuel-efficiency/{id}         # Update efficiency
DELETE /api/v1/fuel-efficiency/{id}         # Delete efficiency record
GET    /api/v1/fuel-efficiency/analysis     # Get efficiency analysis
```

### 8. Dashboard and Analytics Module

#### Dashboard Data
```
GET    /api/v1/dashboard/overview           # Get dashboard overview
GET    /api/v1/dashboard/vehicles           # Get vehicle statistics
GET    /api/v1/dashboard/maintenance        # Get maintenance statistics
GET    /api/v1/dashboard/inventory          # Get inventory statistics
GET    /api/v1/dashboard/fuel               # Get fuel consumption statistics
GET    /api/v1/dashboard/personnel          # Get personnel statistics
GET    /api/v1/dashboard/alerts             # Get system alerts
```

#### Analytics
```
GET    /api/v1/analytics/maintenance-costs  # Maintenance cost analysis
GET    /api/v1/analytics/vehicle-utilization # Vehicle utilization analysis
GET    /api/v1/analytics/fuel-efficiency    # Fuel efficiency trends
GET    /api/v1/analytics/inventory-turnover # Inventory turnover analysis
GET    /api/v1/analytics/personnel-performance # Personnel performance metrics
```

### 9. System Management Module

#### Notifications
```
GET    /api/v1/notifications                # List user notifications
POST   /api/v1/notifications                # Create notification
GET    /api/v1/notifications/{id}           # Get notification details
PUT    /api/v1/notifications/{id}/read      # Mark as read
DELETE /api/v1/notifications/{id}           # Delete notification
POST   /api/v1/notifications/mark-all-read  # Mark all as read
GET    /api/v1/notifications/unread-count   # Get unread count
```

#### System Alerts
```
GET    /api/v1/system-alerts                # List system alerts
POST   /api/v1/system-alerts                # Create system alert
GET    /api/v1/system-alerts/{id}           # Get alert details
PUT    /api/v1/system-alerts/{id}           # Update alert
DELETE /api/v1/system-alerts/{id}           # Delete alert
POST   /api/v1/system-alerts/{id}/resolve   # Resolve alert
```

#### Reports
```
GET    /api/v1/reports                      # List available reports
POST   /api/v1/reports                      # Generate new report
GET    /api/v1/reports/{id}                 # Get report details
DELETE /api/v1/reports/{id}                 # Delete report
GET    /api/v1/reports/{id}/download        # Download report file
POST   /api/v1/reports/{id}/email           # Email report
GET    /api/v1/reports/templates            # Get report templates
```

#### Users & Permissions
```
GET    /api/v1/users                        # List all users
POST   /api/v1/users                        # Create new user
GET    /api/v1/users/{id}                   # Get user details
PUT    /api/v1/users/{id}                   # Update user
DELETE /api/v1/users/{id}                   # Delete user
PUT    /api/v1/users/{id}/permissions       # Update user permissions
PUT    /api/v1/users/{id}/role              # Update user role
PUT    /api/v1/users/{id}/status            # Activate/deactivate user
```

## API Response Standards

### Success Response Format
```json
{
  "success": true,
  "data": {
    // Response data
  },
  "message": "Operation completed successfully",
  "pagination": {
    "current_page": 1,
    "per_page": 15,
    "total": 100,
    "last_page": 7
  }
}
```

### Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "The given data was invalid.",
    "details": {
      "email": ["The email field is required."],
      "password": ["The password must be at least 8 characters."]
    }
  }
}
```

### HTTP Status Codes
- `200` - OK (Successful GET, PUT)
- `201` - Created (Successful POST)
- `204` - No Content (Successful DELETE)
- `400` - Bad Request (Invalid request data)
- `401` - Unauthorized (Authentication required)
- `403` - Forbidden (Insufficient permissions)
- `404` - Not Found (Resource not found)
- `422` - Unprocessable Entity (Validation errors)
- `500` - Internal Server Error (Server errors)

## Request/Response Examples

### Create Employee Example
**Request:**
```http
POST /api/v1/employees
Content-Type: application/json
Authorization: Bearer {token}

{
  "employee_id": "EMP001",
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "position": "Technician",
  "department": "Maintenance",
  "hire_date": "2024-01-15",
  "address": "123 Main St, City, State"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "employee_id": "EMP001",
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "position": "Technician",
    "department": "Maintenance",
    "hire_date": "2024-01-15",
    "status": "active",
    "address": "123 Main St, City, State",
    "created_at": "2024-06-30T21:06:44Z",
    "updated_at": "2024-06-30T21:06:44Z"
  },
  "message": "Employee created successfully"
}
```

### Search Vehicles Example
**Request:**
```http
GET /api/v1/vehicles/search?q=toyota&status=active&type=car&page=1&per_page=10
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "registration_number": "ABC123",
      "make": "Toyota",
      "model": "Camry",
      "year": 2022,
      "type": "car",
      "status": "active",
      "current_mileage": 15000,
      "fuel_type": "gasoline"
    }
  ],
  "pagination": {
    "current_page": 1,
    "per_page": 10,
    "total": 1,
    "last_page": 1
  },
  "message": "Search completed successfully"
}
```

## Middleware & Security

### Authentication Middleware
- JWT token validation
- Token refresh mechanism
- Rate limiting

### Authorization Middleware
- Role-based access control
- Permission-based restrictions
- Resource ownership validation

### Validation Middleware
- Request data validation
- File upload validation
- API rate limiting

### Logging Middleware
- Request/response logging
- Error logging
- Activity tracking

## API Rate Limiting

### General Limits
- 1000 requests per hour per user
- 100 requests per minute per endpoint
- 50 MB maximum request size
- 10 MB maximum file upload

### Special Limits
- Authentication endpoints: 10 requests per minute
- Report generation: 5 requests per hour
- File uploads: 20 requests per hour
