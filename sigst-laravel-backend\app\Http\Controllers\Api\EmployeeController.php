<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreEmployeeRequest;
use App\Http\Requests\UpdateEmployeeRequest;
use App\Models\Employee;
use App\Services\EmployeeService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\QueryBuilder;

class EmployeeController extends Controller
{
    private EmployeeService $employeeService;

    public function __construct(EmployeeService $employeeService)
    {
        $this->employeeService = $employeeService;
    }

    /**
     * Display a listing of employees.
     */
    public function index(Request $request): JsonResponse
    {
        $employees = QueryBuilder::for(Employee::class)
            ->allowedFilters([
                'first_name',
                'last_name', 
                'email',
                'employee_id',
                'department',
                'position',
                'status'
            ])
            ->allowedSorts([
                'first_name',
                'last_name',
                'email',
                'employee_id',
                'department',
                'position',
                'hire_date',
                'created_at'
            ])
            ->allowedIncludes([
                'skills',
                'certifications',
                'trainings',
                'user'
            ])
            ->defaultSort('-created_at')
            ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $employees->items(),
            'pagination' => [
                'current_page' => $employees->currentPage(),
                'per_page' => $employees->perPage(),
                'total' => $employees->total(),
                'last_page' => $employees->lastPage(),
            ],
            'message' => 'Employees retrieved successfully'
        ]);
    }

    /**
     * Store a newly created employee.
     */
    public function store(StoreEmployeeRequest $request): JsonResponse
    {
        try {
            $employee = $this->employeeService->createEmployee($request->validated());

            return response()->json([
                'success' => true,
                'data' => $employee->load(['skills', 'certifications']),
                'message' => 'Employee created successfully'
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'CREATION_FAILED',
                    'message' => 'Failed to create employee',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Display the specified employee.
     */
    public function show(Employee $employee): JsonResponse
    {
        $employee->load([
            'skills' => function ($query) {
                $query->withPivot(['proficiency_level', 'acquired_date', 'expiry_date']);
            },
            'certifications' => function ($query) {
                $query->withPivot(['issue_date', 'expiry_date', 'certificate_number', 'status']);
            },
            'trainings' => function ($query) {
                $query->withPivot(['enrollment_date', 'completion_date', 'status', 'score']);
            },
            'user'
        ]);

        return response()->json([
            'success' => true,
            'data' => $employee,
            'message' => 'Employee retrieved successfully'
        ]);
    }

    /**
     * Update the specified employee.
     */
    public function update(UpdateEmployeeRequest $request, Employee $employee): JsonResponse
    {
        try {
            $updatedEmployee = $this->employeeService->updateEmployee($employee, $request->validated());

            return response()->json([
                'success' => true,
                'data' => $updatedEmployee->load(['skills', 'certifications']),
                'message' => 'Employee updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'UPDATE_FAILED',
                    'message' => 'Failed to update employee',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Remove the specified employee.
     */
    public function destroy(Employee $employee): JsonResponse
    {
        try {
            $this->employeeService->deleteEmployee($employee);

            return response()->json([
                'success' => true,
                'message' => 'Employee deleted successfully'
            ], 204);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'DELETION_FAILED',
                    'message' => 'Failed to delete employee',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Search employees by keywords.
     */
    public function search(Request $request): JsonResponse
    {
        $request->validate([
            'q' => 'required|string|min:2|max:100',
            'per_page' => 'integer|min:1|max:100'
        ]);

        $employees = Employee::search($request->get('q'))
            ->with(['skills', 'certifications'])
            ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $employees->items(),
            'pagination' => [
                'current_page' => $employees->currentPage(),
                'per_page' => $employees->perPage(),
                'total' => $employees->total(),
                'last_page' => $employees->lastPage(),
            ],
            'message' => 'Search completed successfully'
        ]);
    }

    /**
     * Get employee skills.
     */
    public function skills(Employee $employee): JsonResponse
    {
        $skills = $employee->skills()
            ->withPivot(['proficiency_level', 'acquired_date', 'expiry_date'])
            ->get();

        return response()->json([
            'success' => true,
            'data' => $skills,
            'message' => 'Employee skills retrieved successfully'
        ]);
    }

    /**
     * Get employee certifications.
     */
    public function certifications(Employee $employee): JsonResponse
    {
        $certifications = $employee->certifications()
            ->withPivot(['issue_date', 'expiry_date', 'certificate_number', 'status'])
            ->get();

        return response()->json([
            'success' => true,
            'data' => $certifications,
            'message' => 'Employee certifications retrieved successfully'
        ]);
    }

    /**
     * Get employee trainings.
     */
    public function trainings(Employee $employee): JsonResponse
    {
        $trainings = $employee->trainings()
            ->withPivot(['enrollment_date', 'completion_date', 'status', 'score', 'instructor'])
            ->get();

        return response()->json([
            'success' => true,
            'data' => $trainings,
            'message' => 'Employee trainings retrieved successfully'
        ]);
    }

    /**
     * Assign skill to employee.
     */
    public function assignSkill(Request $request, Employee $employee): JsonResponse
    {
        $request->validate([
            'skill_id' => 'required|exists:skills,id',
            'proficiency_level' => 'required|in:beginner,intermediate,advanced,expert',
            'acquired_date' => 'required|date',
            'expiry_date' => 'nullable|date|after:acquired_date'
        ]);

        try {
            $employee->skills()->attach($request->skill_id, [
                'proficiency_level' => $request->proficiency_level,
                'acquired_date' => $request->acquired_date,
                'expiry_date' => $request->expiry_date,
                'created_at' => now(),
                'updated_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Skill assigned to employee successfully'
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'ASSIGNMENT_FAILED',
                    'message' => 'Failed to assign skill to employee',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Remove skill from employee.
     */
    public function removeSkill(Employee $employee, int $skillId): JsonResponse
    {
        try {
            $employee->skills()->detach($skillId);

            return response()->json([
                'success' => true,
                'message' => 'Skill removed from employee successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'REMOVAL_FAILED',
                    'message' => 'Failed to remove skill from employee',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Get employees with expiring certifications.
     */
    public function expiringCertifications(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        
        $employees = Employee::withExpiringCertifications($days)
            ->with(['certifications' => function ($query) use ($days) {
                $query->wherePivot('expiry_date', '<=', now()->addDays($days))
                      ->wherePivot('status', 'active')
                      ->withPivot(['expiry_date', 'certificate_number', 'status']);
            }])
            ->get();

        return response()->json([
            'success' => true,
            'data' => $employees,
            'message' => 'Employees with expiring certifications retrieved successfully'
        ]);
    }

    /**
     * Get department statistics.
     */
    public function departmentStats(): JsonResponse
    {
        $stats = Employee::selectRaw('department, count(*) as total, 
                                   sum(case when status = ? then 1 else 0 end) as active',
                                   ['active'])
            ->groupBy('department')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $stats,
            'message' => 'Department statistics retrieved successfully'
        ]);
    }

    /**
     * Get position statistics.
     */
    public function positionStats(): JsonResponse
    {
        $stats = Employee::selectRaw('position, count(*) as total')
            ->whereNotNull('position')
            ->groupBy('position')
            ->orderBy('total', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $stats,
            'message' => 'Position statistics retrieved successfully'
        ]);
    }
}
