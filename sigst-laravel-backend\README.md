# SIGST Laravel Backend

Technical Services Management Information System (SIGST) - Laravel Backend API

## Overview

This is the backend API for the SIGST (Technical Services Management Information System) built with Laravel 10.x framework. It provides comprehensive management capabilities for:

- Personnel Management (Employees, Skills, Certifications, Training)
- Vehicle Fleet Management
- Maintenance Management
- Spare Parts Inventory
- Tool Management
- Product Management
- Fuel Management
- Real-time Dashboards and Analytics
- Report Generation

## System Requirements

- PHP >= 8.1
- Composer
- PostgreSQL >= 12.0
- Node.js >= 16.x (for Laravel Mix)
- Redis (optional, for caching and queues)

## Installation Instructions

### 1. Install PHP and Required Extensions

#### Ubuntu/Debian:
```bash
sudo apt update
sudo apt install -y php8.1 php8.1-cli php8.1-common php8.1-mysql php8.1-zip php8.1-gd php8.1-mbstring php8.1-curl php8.1-xml php8.1-bcmath php8.1-pgsql php8.1-redis php8.1-intl
```

#### CentOS/RHEL:
```bash
sudo yum install -y php81 php81-php-cli php81-php-common php81-php-mysqlnd php81-php-zip php81-php-gd php81-php-mbstring php81-php-curl php81-php-xml php81-php-bcmath php81-php-pgsql
```

#### macOS (using Homebrew):
```bash
brew install php@8.1
brew install composer
```

### 2. Install Composer

```bash
# Download and install Composer globally
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
```

### 3. Install PostgreSQL

#### Ubuntu/Debian:
```bash
sudo apt install -y postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

#### macOS:
```bash
brew install postgresql
brew services start postgresql
```

### 4. Clone and Setup Project

```bash
# Navigate to the project directory
cd /workspace/sigst-laravel-backend

# Install PHP dependencies
composer install

# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate
```

### 5. Database Configuration

1. **Create PostgreSQL Database:**
```bash
sudo -u postgres psql
CREATE DATABASE sigst_database;
CREATE USER sigst_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE sigst_database TO sigst_user;
\q
```

2. **Update .env file:**
```env
DB_CONNECTION=pgsql
DB_HOST=127.0.0.1
DB_PORT=5432
DB_DATABASE=sigst_database
DB_USERNAME=sigst_user
DB_PASSWORD=your_password
```

### 6. Run Migrations and Seeders

```bash
# Run database migrations
php artisan migrate

# Seed the database with initial data
php artisan db:seed

# Or run migrations with seeding in one command
php artisan migrate:fresh --seed
```

### 7. Configure Authentication

```bash
# Install Laravel Sanctum for API authentication
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"
php artisan migrate
```

### 8. Configure File Storage

```bash
# Create symbolic link for public storage
php artisan storage:link

# Set proper permissions
sudo chmod -R 775 storage
sudo chmod -R 775 bootstrap/cache
```

### 9. Start Development Server

```bash
# Start Laravel development server
php artisan serve

# Or specify custom host and port
php artisan serve --host=0.0.0.0 --port=8000
```

The API will be available at: `http://localhost:8000/api/v1`

## Environment Configuration

### Required Environment Variables

```env
# Application
APP_NAME="SIGST Backend"
APP_ENV=local
APP_KEY=base64:your_generated_key
APP_DEBUG=true
APP_URL=http://localhost:8000

# Database
DB_CONNECTION=pgsql
DB_HOST=127.0.0.1
DB_PORT=5432
DB_DATABASE=sigst_database
DB_USERNAME=sigst_user
DB_PASSWORD=your_password

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=your_username
MAIL_PASSWORD=your_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# File Upload Settings
MAX_FILE_SIZE=10240
ALLOWED_FILE_TYPES=pdf,doc,docx,jpg,jpeg,png,gif

# JWT Settings (if using JWT)
JWT_SECRET=your_jwt_secret
JWT_TTL=60
JWT_REFRESH_TTL=20160
```

## Database Schema

The application uses PostgreSQL with the following main tables:

### Core Tables:
- `employees` - Employee information
- `skills` - Available skills
- `employee_skills` - Employee-skill relationships
- `certifications` - Available certifications
- `employee_certifications` - Employee certifications
- `trainings` - Training programs
- `employee_trainings` - Training enrollments
- `vehicles` - Vehicle fleet
- `vehicle_documents` - Vehicle documentation
- `vehicle_interventions` - Maintenance history
- `vehicle_accidents` - Accident records
- `maintenance_schedules` - Preventive maintenance
- `maintenance_requests` - Maintenance requests
- `maintenance_operations` - Maintenance operations
- `spare_parts` - Parts catalog
- `parts_inventory` - Stock levels
- `parts_movements` - Stock movements
- `tools` - Tool inventory
- `tool_assignments` - Tool assignments
- `products` - Product catalog
- `product_inventory` - Product stock
- `fuel_stations` - Fuel stations
- `fuel_transactions` - Fuel consumption
- `users` - System users
- `notifications` - User notifications

## API Documentation

### Base URL
All API endpoints are prefixed with `/api/v1`

### Authentication
The API uses Laravel Sanctum for authentication. Include the bearer token in the Authorization header:

```
Authorization: Bearer your_token_here
```

### Standard Response Format

#### Success Response:
```json
{
  "success": true,
  "data": { ... },
  "message": "Operation completed successfully",
  "pagination": { ... } // Only for paginated responses
}
```

#### Error Response:
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Error description",
    "details": { ... } // Validation errors or additional details
  }
}
```

### Key API Endpoints

#### Authentication:
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/logout` - User logout
- `GET /api/v1/auth/me` - Get current user
- `POST /api/v1/auth/change-password` - Change password

#### Employees:
- `GET /api/v1/employees` - List employees
- `POST /api/v1/employees` - Create employee
- `GET /api/v1/employees/{id}` - Get employee details
- `PUT /api/v1/employees/{id}` - Update employee
- `DELETE /api/v1/employees/{id}` - Delete employee
- `GET /api/v1/employees/search?q=keyword` - Search employees

#### Vehicles:
- `GET /api/v1/vehicles` - List vehicles
- `POST /api/v1/vehicles` - Create vehicle
- `GET /api/v1/vehicles/{id}` - Get vehicle details
- `GET /api/v1/vehicles/maintenance-due` - Get vehicles due for maintenance

#### And many more endpoints for all modules...

## Available Artisan Commands

```bash
# Database operations
php artisan migrate                 # Run migrations
php artisan migrate:fresh --seed    # Fresh migration with seeding
php artisan db:seed                # Run seeders

# Cache management
php artisan cache:clear            # Clear application cache
php artisan config:cache           # Cache configuration
php artisan route:cache            # Cache routes

# Queue management
php artisan queue:work             # Start queue worker
php artisan queue:restart          # Restart queue workers

# Custom commands (to be implemented)
php artisan sigst:generate-reports    # Generate scheduled reports
php artisan sigst:check-maintenance   # Check maintenance schedules
php artisan sigst:send-notifications  # Send pending notifications
```

## Testing

```bash
# Run all tests
php artisan test

# Run specific test suite
php artisan test --testsuite=Feature
php artisan test --testsuite=Unit

# Run tests with coverage
php artisan test --coverage
```

## Deployment

### Production Environment Setup

1. **Set Production Environment:**
```env
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com
```

2. **Optimize Application:**
```bash
composer install --optimize-autoloader --no-dev
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

3. **Set Proper Permissions:**
```bash
sudo chown -R www-data:www-data /path/to/sigst-backend
sudo chmod -R 755 /path/to/sigst-backend
sudo chmod -R 775 /path/to/sigst-backend/storage
sudo chmod -R 775 /path/to/sigst-backend/bootstrap/cache
```

4. **Configure Web Server (Nginx Example):**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/sigst-backend/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
```

## Monitoring and Logging

### Log Files Location:
- Application logs: `storage/logs/laravel.log`
- Error logs: Check web server error logs
- Queue logs: `storage/logs/queue.log`

### Monitoring Commands:
```bash
# Monitor logs in real-time
tail -f storage/logs/laravel.log

# Check application status
php artisan route:list
php artisan about
```

## Security Considerations

1. **API Rate Limiting**: Configured in `config/sanctum.php`
2. **CORS Configuration**: Set in `config/cors.php`
3. **File Upload Security**: Validated file types and sizes
4. **SQL Injection Prevention**: Using Eloquent ORM and parameterized queries
5. **XSS Protection**: Input validation and output escaping
6. **Authentication**: Secure token-based authentication

## Troubleshooting

### Common Issues:

1. **Permission Errors:**
```bash
sudo chmod -R 775 storage bootstrap/cache
sudo chown -R www-data:www-data storage bootstrap/cache
```

2. **Database Connection Issues:**
- Check PostgreSQL service is running
- Verify database credentials in `.env`
- Check firewall settings

3. **Composer Issues:**
```bash
composer clear-cache
composer install --no-cache
```

4. **Migration Errors:**
```bash
php artisan migrate:status
php artisan migrate:reset
php artisan migrate
```

## Support and Documentation

- Laravel Documentation: https://laravel.com/docs
- PostgreSQL Documentation: https://www.postgresql.org/docs/
- API Testing: Use Postman collection (to be provided)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Write tests
5. Submit a pull request

## License

This project is proprietary software. All rights reserved.
