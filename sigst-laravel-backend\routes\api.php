<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\EmployeeController;
use App\Http\Controllers\Api\VehicleController;
use App\Http\Controllers\Api\MaintenanceController;
use App\Http\Controllers\Api\SparePartsController;
use App\Http\Controllers\Api\ToolController;
use App\Http\Controllers\Api\ProductController;
use App\Http\Controllers\Api\FuelController;
use App\Http\Controllers\Api\DashboardController;
use App\Http\Controllers\Api\NotificationController;
use App\Http\Controllers\Api\ReportController;
use App\Http\Controllers\Api\UserController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

// Authentication Routes (Public)
Route::prefix('auth')->group(function () {
    Route::post('login', [AuthController::class, 'login']);
    Route::post('register', [AuthController::class, 'register']);
    Route::post('forgot-password', [AuthController::class, 'forgotPassword']);
    Route::post('reset-password', [AuthController::class, 'resetPassword']);
});

// Protected Routes
Route::middleware(['auth:sanctum'])->group(function () {
    
    // Authentication Routes (Protected)
    Route::prefix('auth')->group(function () {
        Route::post('logout', [AuthController::class, 'logout']);
        Route::post('refresh', [AuthController::class, 'refresh']);
        Route::get('me', [AuthController::class, 'me']);
        Route::post('change-password', [AuthController::class, 'changePassword']);
    });

    // Personnel Management Routes
    Route::prefix('employees')->group(function () {
        Route::get('/', [EmployeeController::class, 'index']);
        Route::post('/', [EmployeeController::class, 'store']);
        Route::get('search', [EmployeeController::class, 'search']);
        Route::get('expiring-certifications', [EmployeeController::class, 'expiringCertifications']);
        Route::get('department-stats', [EmployeeController::class, 'departmentStats']);
        Route::get('position-stats', [EmployeeController::class, 'positionStats']);
        
        Route::prefix('{employee}')->group(function () {
            Route::get('/', [EmployeeController::class, 'show']);
            Route::put('/', [EmployeeController::class, 'update']);
            Route::delete('/', [EmployeeController::class, 'destroy']);
            Route::get('skills', [EmployeeController::class, 'skills']);
            Route::get('certifications', [EmployeeController::class, 'certifications']);
            Route::get('trainings', [EmployeeController::class, 'trainings']);
            Route::post('skills/{skill}', [EmployeeController::class, 'assignSkill']);
            Route::delete('skills/{skill}', [EmployeeController::class, 'removeSkill']);
        });
    });

    // Skills Management
    Route::apiResource('skills', SkillController::class);

    // Certifications Management
    Route::prefix('certifications')->group(function () {
        Route::get('/', [CertificationController::class, 'index']);
        Route::post('/', [CertificationController::class, 'store']);
        Route::get('expiring', [CertificationController::class, 'expiring']);
        Route::get('{certification}', [CertificationController::class, 'show']);
        Route::put('{certification}', [CertificationController::class, 'update']);
        Route::delete('{certification}', [CertificationController::class, 'destroy']);
        
        // Employee certification assignments
        Route::post('{employee}/certifications', [CertificationController::class, 'assignToEmployee']);
        Route::put('{employee}/certifications/{certification}', [CertificationController::class, 'updateEmployeeCertification']);
    });

    // Training Management
    Route::prefix('trainings')->group(function () {
        Route::get('/', [TrainingController::class, 'index']);
        Route::post('/', [TrainingController::class, 'store']);
        Route::get('{training}', [TrainingController::class, 'show']);
        Route::put('{training}', [TrainingController::class, 'update']);
        Route::delete('{training}', [TrainingController::class, 'destroy']);
        Route::post('{training}/enroll', [TrainingController::class, 'enrollEmployees']);
        Route::put('{training}/complete', [TrainingController::class, 'markComplete']);
    });

    // Vehicle Management Routes
    Route::prefix('vehicles')->group(function () {
        Route::get('/', [VehicleController::class, 'index']);
        Route::post('/', [VehicleController::class, 'store']);
        Route::get('search', [VehicleController::class, 'search']);
        Route::get('maintenance-due', [VehicleController::class, 'maintenanceDue']);
        
        Route::prefix('{vehicle}')->group(function () {
            Route::get('/', [VehicleController::class, 'show']);
            Route::put('/', [VehicleController::class, 'update']);
            Route::delete('/', [VehicleController::class, 'destroy']);
            Route::get('documents', [VehicleController::class, 'documents']);
            Route::get('interventions', [VehicleController::class, 'interventions']);
            Route::get('accidents', [VehicleController::class, 'accidents']);
        });
    });

    // Vehicle Documents
    Route::prefix('vehicle-documents')->group(function () {
        Route::get('/', [VehicleDocumentController::class, 'index']);
        Route::post('/', [VehicleDocumentController::class, 'store']);
        Route::get('expiring', [VehicleDocumentController::class, 'expiring']);
        Route::get('{document}', [VehicleDocumentController::class, 'show']);
        Route::put('{document}', [VehicleDocumentController::class, 'update']);
        Route::delete('{document}', [VehicleDocumentController::class, 'destroy']);
        Route::post('{document}/upload', [VehicleDocumentController::class, 'uploadFile']);
    });

    // Vehicle Interventions
    Route::apiResource('vehicle-interventions', VehicleInterventionController::class);

    // Vehicle Accidents
    Route::apiResource('vehicle-accidents', VehicleAccidentController::class);

    // Maintenance Management Routes
    Route::prefix('maintenance-schedules')->group(function () {
        Route::get('/', [MaintenanceScheduleController::class, 'index']);
        Route::post('/', [MaintenanceScheduleController::class, 'store']);
        Route::get('due', [MaintenanceScheduleController::class, 'overdue']);
        Route::post('bulk', [MaintenanceScheduleController::class, 'bulkCreate']);
        Route::get('{schedule}', [MaintenanceScheduleController::class, 'show']);
        Route::put('{schedule}', [MaintenanceScheduleController::class, 'update']);
        Route::delete('{schedule}', [MaintenanceScheduleController::class, 'destroy']);
    });

    Route::prefix('maintenance-requests')->group(function () {
        Route::get('/', [MaintenanceRequestController::class, 'index']);
        Route::post('/', [MaintenanceRequestController::class, 'store']);
        Route::get('my', [MaintenanceRequestController::class, 'myRequests']);
        Route::get('{request}', [MaintenanceRequestController::class, 'show']);
        Route::put('{request}', [MaintenanceRequestController::class, 'update']);
        Route::delete('{request}', [MaintenanceRequestController::class, 'destroy']);
        Route::post('{request}/approve', [MaintenanceRequestController::class, 'approve']);
        Route::post('{request}/assign', [MaintenanceRequestController::class, 'assignTechnician']);
        Route::post('{request}/complete', [MaintenanceRequestController::class, 'complete']);
    });

    Route::prefix('maintenance-operations')->group(function () {
        Route::get('/', [MaintenanceOperationController::class, 'index']);
        Route::post('/', [MaintenanceOperationController::class, 'store']);
        Route::get('{operation}', [MaintenanceOperationController::class, 'show']);
        Route::put('{operation}', [MaintenanceOperationController::class, 'update']);
        Route::delete('{operation}', [MaintenanceOperationController::class, 'destroy']);
        Route::post('{operation}/quality-check', [MaintenanceOperationController::class, 'qualityCheck']);
    });

    // Spare Parts Management Routes
    Route::prefix('spare-parts')->group(function () {
        Route::get('/', [SparePartsController::class, 'index']);
        Route::post('/', [SparePartsController::class, 'store']);
        Route::get('search', [SparePartsController::class, 'search']);
        Route::get('low-stock', [SparePartsController::class, 'lowStock']);
        Route::get('{part}', [SparePartsController::class, 'show']);
        Route::put('{part}', [SparePartsController::class, 'update']);
        Route::delete('{part}', [SparePartsController::class, 'destroy']);
        Route::get('{part}/movements', [SparePartsController::class, 'movements']);
    });

    Route::prefix('parts-inventory')->group(function () {
        Route::get('/', [PartsInventoryController::class, 'index']);
        Route::put('{inventory}', [PartsInventoryController::class, 'update']);
        Route::get('alerts', [PartsInventoryController::class, 'alerts']);
        Route::post('adjustment', [PartsInventoryController::class, 'adjustment']);
    });

    Route::prefix('parts-movements')->group(function () {
        Route::get('/', [PartsMovementController::class, 'index']);
        Route::post('/', [PartsMovementController::class, 'store']);
        Route::post('bulk', [PartsMovementController::class, 'bulkMovements']);
        Route::get('{movement}', [PartsMovementController::class, 'show']);
        Route::put('{movement}', [PartsMovementController::class, 'update']);
        Route::delete('{movement}', [PartsMovementController::class, 'destroy']);
    });

    Route::apiResource('parts-suppliers', PartsSupplierController::class);

    // Tool Management Routes
    Route::prefix('tools')->group(function () {
        Route::get('/', [ToolController::class, 'index']);
        Route::post('/', [ToolController::class, 'store']);
        Route::get('search', [ToolController::class, 'search']);
        Route::get('available', [ToolController::class, 'available']);
        Route::get('calibration-due', [ToolController::class, 'calibrationDue']);
        Route::get('{tool}', [ToolController::class, 'show']);
        Route::put('{tool}', [ToolController::class, 'update']);
        Route::delete('{tool}', [ToolController::class, 'destroy']);
        Route::get('{tool}/assignments', [ToolController::class, 'assignments']);
    });

    Route::prefix('tool-assignments')->group(function () {
        Route::get('/', [ToolAssignmentController::class, 'index']);
        Route::post('/', [ToolAssignmentController::class, 'store']);
        Route::get('overdue', [ToolAssignmentController::class, 'overdue']);
        Route::get('{assignment}', [ToolAssignmentController::class, 'show']);
        Route::put('{assignment}', [ToolAssignmentController::class, 'update']);
        Route::delete('{assignment}', [ToolAssignmentController::class, 'destroy']);
        Route::post('{assignment}/return', [ToolAssignmentController::class, 'returnTool']);
    });

    // Product Management Routes
    Route::prefix('products')->group(function () {
        Route::get('/', [ProductController::class, 'index']);
        Route::post('/', [ProductController::class, 'store']);
        Route::get('search', [ProductController::class, 'search']);
        Route::get('expiring', [ProductController::class, 'expiring']);
        Route::get('{product}', [ProductController::class, 'show']);
        Route::put('{product}', [ProductController::class, 'update']);
        Route::delete('{product}', [ProductController::class, 'destroy']);
        Route::get('{product}/inventory', [ProductController::class, 'inventory']);
        Route::get('{product}/consumption', [ProductController::class, 'consumption']);
    });

    Route::prefix('product-inventory')->group(function () {
        Route::get('/', [ProductInventoryController::class, 'index']);
        Route::post('/', [ProductInventoryController::class, 'store']);
        Route::get('alerts', [ProductInventoryController::class, 'alerts']);
        Route::get('{inventory}', [ProductInventoryController::class, 'show']);
        Route::put('{inventory}', [ProductInventoryController::class, 'update']);
        Route::delete('{inventory}', [ProductInventoryController::class, 'destroy']);
    });

    Route::prefix('product-consumption')->group(function () {
        Route::get('/', [ProductConsumptionController::class, 'index']);
        Route::post('/', [ProductConsumptionController::class, 'store']);
        Route::get('reports', [ProductConsumptionController::class, 'reports']);
        Route::get('{consumption}', [ProductConsumptionController::class, 'show']);
        Route::put('{consumption}', [ProductConsumptionController::class, 'update']);
        Route::delete('{consumption}', [ProductConsumptionController::class, 'destroy']);
    });

    // Fuel Management Routes
    Route::apiResource('fuel-stations', FuelStationController::class);

    Route::prefix('fuel-transactions')->group(function () {
        Route::get('/', [FuelTransactionController::class, 'index']);
        Route::post('/', [FuelTransactionController::class, 'store']);
        Route::get('reports', [FuelTransactionController::class, 'reports']);
        Route::get('summary', [FuelTransactionController::class, 'summary']);
        Route::get('{transaction}', [FuelTransactionController::class, 'show']);
        Route::put('{transaction}', [FuelTransactionController::class, 'update']);
        Route::delete('{transaction}', [FuelTransactionController::class, 'destroy']);
    });

    Route::prefix('fuel-efficiency')->group(function () {
        Route::get('/', [FuelEfficiencyController::class, 'index']);
        Route::post('/', [FuelEfficiencyController::class, 'store']);
        Route::get('analysis', [FuelEfficiencyController::class, 'analysis']);
        Route::get('{efficiency}', [FuelEfficiencyController::class, 'show']);
        Route::put('{efficiency}', [FuelEfficiencyController::class, 'update']);
        Route::delete('{efficiency}', [FuelEfficiencyController::class, 'destroy']);
    });

    // Dashboard Routes
    Route::prefix('dashboard')->group(function () {
        Route::get('overview', [DashboardController::class, 'overview']);
        Route::get('vehicles', [DashboardController::class, 'vehicles']);
        Route::get('maintenance', [DashboardController::class, 'maintenance']);
        Route::get('inventory', [DashboardController::class, 'inventory']);
        Route::get('fuel', [DashboardController::class, 'fuel']);
        Route::get('personnel', [DashboardController::class, 'personnel']);
        Route::get('alerts', [DashboardController::class, 'alerts']);
    });

    // Analytics Routes
    Route::prefix('analytics')->group(function () {
        Route::get('maintenance-costs', [AnalyticsController::class, 'maintenanceCosts']);
        Route::get('vehicle-utilization', [AnalyticsController::class, 'vehicleUtilization']);
        Route::get('fuel-efficiency', [AnalyticsController::class, 'fuelEfficiency']);
        Route::get('inventory-turnover', [AnalyticsController::class, 'inventoryTurnover']);
        Route::get('personnel-performance', [AnalyticsController::class, 'personnelPerformance']);
    });

    // Notification Routes
    Route::prefix('notifications')->group(function () {
        Route::get('/', [NotificationController::class, 'index']);
        Route::post('/', [NotificationController::class, 'store']);
        Route::get('unread-count', [NotificationController::class, 'unreadCount']);
        Route::post('mark-all-read', [NotificationController::class, 'markAllRead']);
        Route::get('{notification}', [NotificationController::class, 'show']);
        Route::put('{notification}/read', [NotificationController::class, 'markAsRead']);
        Route::delete('{notification}', [NotificationController::class, 'destroy']);
    });

    // System Alert Routes
    Route::prefix('system-alerts')->group(function () {
        Route::get('/', [SystemAlertController::class, 'index']);
        Route::post('/', [SystemAlertController::class, 'store']);
        Route::get('{alert}', [SystemAlertController::class, 'show']);
        Route::put('{alert}', [SystemAlertController::class, 'update']);
        Route::delete('{alert}', [SystemAlertController::class, 'destroy']);
        Route::post('{alert}/resolve', [SystemAlertController::class, 'resolve']);
    });

    // Report Routes
    Route::prefix('reports')->group(function () {
        Route::get('/', [ReportController::class, 'index']);
        Route::post('/', [ReportController::class, 'generate']);
        Route::get('templates', [ReportController::class, 'templates']);
        Route::get('{report}', [ReportController::class, 'show']);
        Route::delete('{report}', [ReportController::class, 'destroy']);
        Route::get('{report}/download', [ReportController::class, 'download']);
        Route::post('{report}/email', [ReportController::class, 'email']);
    });

    // User Management Routes
    Route::prefix('users')->group(function () {
        Route::get('/', [UserController::class, 'index']);
        Route::post('/', [UserController::class, 'store']);
        Route::get('{user}', [UserController::class, 'show']);
        Route::put('{user}', [UserController::class, 'update']);
        Route::delete('{user}', [UserController::class, 'destroy']);
        Route::put('{user}/permissions', [UserController::class, 'updatePermissions']);
        Route::put('{user}/role', [UserController::class, 'updateRole']);
        Route::put('{user}/status', [UserController::class, 'updateStatus']);
    });
});

// Health Check Route
Route::get('health', function () {
    return response()->json([
        'success' => true,
        'message' => 'SIGST API is running',
        'timestamp' => now(),
        'version' => '1.0.0'
    ]);
});
